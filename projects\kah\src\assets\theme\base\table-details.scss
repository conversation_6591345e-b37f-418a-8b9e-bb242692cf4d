.bck-table-details {
    .tab-content {
        label {
            line-height: 20px;
            font-size: var(--snjy-font-size-0-8);
            font-weight: var(--snjy-font-weight-medium);
            color: #303338;
            margin: 0 0 6px 0;
            display: block;
            padding: 0 0 0 25px;
            position: relative;

            &::before {
                position: absolute;
                content: "\f1df";
                font-family: "Material Icons Outlined";
                top: 0;
                left: 5px;
                bottom: 0;
                margin: auto 0;
                font-size: var(--snjy-font-size-1);
                color: #0086f8;
                font-weight: var(--snjy-font-weight-normal);
                line-height: 20px;
                height: -moz-fit-content;
                height: fit-content;
            }
        }

        .form-control,
        .form-select {
            margin: 0 0 14px 0;
            padding: 0 14px;
            height: 40px;
            background: var(--snjy-font-color-primary);
            border-radius: 8px;
            font-size: var(--snjy-font-size-0-8);
            font-weight: var(--snjy-font-weight-medium);
            border: 1px solid #bbc2c961;
            box-shadow: 0 1px 2px #2a343b14;
            appearance: auto !important;
            color: #5b5f78;
        }

        .angular-editor-toolbar {
            background: var(--snjy-font-color-primary) !important;
        }

        .angular-editor-textarea {
            background: var(--snjy-font-color-primary) !important;

            blockquote {
                border: none !important;
            }
        }

        h5 {
            line-height: normal;
            font-size: var(--snjy-font-size-1-125);
            font-weight: var(--snjy-font-weight-medium);
            color: var(--snjy-button-color-primary);
        }
    }

    .accordion {
        position: relative;

        .accordion-item {
            margin: 0 0 5px 0;
            border-radius: 8px;
            overflow: hidden;

            .accordion-header {
                border: none !important;

                button.accordion-button {
                    background: #b2dcff;
                    color: var(--snjy-color-dark-secondary) !important;
                    font-size: var(--snjy-font-size-0-9);
                    font-weight: var(--snjy-font-weight-medium);
                    outline: none;
                    box-shadow: none;
                    position: relative;
                    padding-left: 32px;

                    &::before {
                        position: absolute;
                        content: "\e92f";
                        font-family: "Material Icons Outlined";
                        top: 0;
                        left: 13px;
                        bottom: 0;
                        margin: auto 0;
                        font-size: var(--snjy-font-size-0-9);
                        color: #272727;
                        font-weight: var(--snjy-font-weight-normal);
                        line-height: 20px;
                        height: fit-content;
                    }
                }
            }
        }
    }
}