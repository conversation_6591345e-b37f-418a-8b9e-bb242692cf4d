import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { forkJoin, map, of, tap } from 'rxjs';
import { StoreFrontAPIConstant } from '../../constants/api.constants';
import { AuthService } from 'ng-snjya';

@Injectable({
  providedIn: 'root'
})
export class CapmaignCenterService {

  cache: { [key in string]: any } = {};

  constructor(
    private http: HttpClient,
    private authService: AuthService
  ) { }

  fetchAll() {
    if (this.cache['all']) {
      return of(this.cache['all']);
    }
    return forkJoin({
      times: this.fetchTime(),
      dms: this.fetchDMs(),
      offers: this.fetchActivationOffers(),
      pageDetails: this.fetchPageDetails(),
    }).pipe(
      tap(data => this.cache['all'] = data)
    )
  }

  fetchPageDetails() {
    return this.http.get<any>(
      `${StoreFrontAPIConstant.CampaignCenterPage}`
    ).pipe(map(data => data.data.attributes));
  }

  createMarketingTicket(data: any) {
    return this.http.post<any>(
      `${StoreFrontAPIConstant.CreateMarketingTicket}`,
      data
    );
  }

  fetchActivationRequests(data: any) {
    const params = new HttpParams().appendAll({
      ...data,
      ...{ search: data.search },
    });
    return this.http.get<any>(StoreFrontAPIConstant.Activation, {
      params,
    });
  }

  fetchTime() {
    return this.http.get<any>(
      `${StoreFrontAPIConstant.Time}`
    ).pipe(map(data => data.data));
  }

  fetchDMs() {
    let params = new HttpParams();
    params = params.set('userId', this.authService.userDetail.id)
    return this.http.get<any>(
      `${StoreFrontAPIConstant.DMs}`, { params }
    ).pipe(map(data => data.data));
  }

  fetchActivationOffers() {
    return this.http.get<any>(
      `${StoreFrontAPIConstant.Activation_Offers}`
    ).pipe(map(data => data.data));
  }

}
