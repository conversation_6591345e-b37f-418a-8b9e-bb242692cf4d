<section class="category-and-sidebar-sec">
    <div class="category-and-sidebar-body">
        <div class="category-part">
            <h2 class="text-center">{{data?.Category_title}}</h2>
            <div class="category-list">
                <ng-container *ngFor="let category_item of data?.Category_Box">
                    <div class="category-item">
                        <div class="c-item-body">
                            <div class="c-item-icon">
                                <img [src]="category_item?.category_box_img.data.attributes.url" alt="" />
                            </div>
                            <div class="c-item-title">{{category_item?.category_box_title}}</div>
                            <div class="c-item-p">{{category_item?.category_box_p}}</div>
                            <div class="c-item-link">
                                <a [routerLink]="[category_item?.category_link_url]">
                                    {{category_item?.category_link_text}}
                                    <span class="material-symbols-outlined">{{category_item?.category_link_icon}}</span>
                                </a>
                            </div>
                        </div>
                    </div>
                </ng-container>
            </div>
        </div>
        <ng-container *ngIf="homeSidebar?.home_sidebar_list.data.attributes.h_sidebar_list.length">
            <div class="sidebar-part">
                <ng-container *ngFor="let sidebarListBox of homeSidebar?.home_sidebar_list.data.attributes.h_sidebar_list">
                    <div class="home-sidebar-box">
                        <div class="home-sidebar-box-title">{{sidebarListBox?.sidebar_list_title}}</div>
                        <div class="home-sidebar-box-list">
                            <ul>
                                <ng-container *ngFor="let sidebarList of sidebarListBox?.sidebar_list">
                                    <li *ngIf="sidebarList?.sidebar_list_link">
                                        <a [href]="[sidebarList?.sidebar_list_link]" target="_blank">
                                            <span
                                                class="material-symbols-outlined">{{sidebarList?.sidebar_list_icon}}</span>
                                            <span class="hsb-text">{{sidebarList?.sidebar_list_name}}</span>
                                            <span class="hsb-date">{{sidebarList?.sidebar_list_date}}</span>
                                        </a>
                                    </li>
                                </ng-container>
                            </ul>
                        </div>
                    </div>
                </ng-container>
            </div>
        </ng-container>

    </div>
</section>