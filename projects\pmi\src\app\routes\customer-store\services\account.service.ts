import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { StoreFrontAPIConstant } from '../constants/api.constants';
import { Observable, of, tap } from 'rxjs';

@Injectable({
    providedIn: 'root'
})
export class AccountService {

    constructor(private http: HttpClient) { }

    getFavorites(): Observable<any> {
        const url = `${StoreFrontAPIConstant.Favorite_articles}`;
        return this.http.get<any[]>(url);
    }

    getFavoritesById(id: string): Observable<any> {
        const url = `${StoreFrontAPIConstant.Favorite_articles}/${id}`;
        return this.http.get<any[]>(url);
    }

    makeFavorite(data: any): Observable<any> {
        return this.http.post<any>(`${StoreFrontAPIConstant.Favorite_articles}`, data);
    }

    deleteFavorite(id: string): Observable<any> {
        return this.http.delete<any>(`${StoreFrontAPIConstant.Favorite_articles}/${id}`);
    }

}
