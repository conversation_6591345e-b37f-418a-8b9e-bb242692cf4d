import { Component, Input, OnInit } from '@angular/core';
import { StoreFrontAPIConstant } from '../../constants/api.constants';
import { HomeService } from '../../services/home.service';
import { ComponentNameConstants } from '../../constants/components.contants';
import { OwlOptions } from 'ngx-owl-carousel-o';

@Component({
  selector: 'app-pizzawork',
  templateUrl: './pizzawork.component.html',
  styleUrls: ['./pizzawork.component.scss']
})
export class PizzaworkComponent implements OnInit {

  @Input() data: any;
  @Input() set allData(data: any) {
    if (data) {
      this.signUpRenewelData = this.service.getDataByComponentName(data, ComponentNameConstants.SignageRenewal);
      this.watchTheReplay = this.service.getDataByComponentName(data, ComponentNameConstants.WatchTheReplay);
    }
  }

  signUpRenewelData: any = {};
  watchTheReplay: any = {};

  imgPath: string = StoreFrontAPIConstant.IMG_URL;

  constructor(private service: HomeService) { }

  ngOnInit(): void { }

  customOptions: OwlOptions = {
    loop: true,
    autoplay: false,
    dots: false,
    nav: true,
    navSpeed: 3000,
    autoplayTimeout: 3000,
    autoplaySpeed: 3000,
    animateOut: 'fadeOut',
    navText: ["<img src='/assets/images/arrow_back.svg' />", "<img src='/assets/images/arrow_forward.svg' />"],
    responsive: {
      0: {
        items: 1,
      },
      600: {
        items: 1,
      },
      1000: {
        items: 1,
      },
    },
  };

}
