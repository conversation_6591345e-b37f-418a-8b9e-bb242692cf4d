import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { SearchArticlesRoutingModule } from './search-articles-routing.module';
import { SearchArticlesComponent } from './search-articles.component';
import { NgbPaginationModule } from '@ng-bootstrap/ng-bootstrap';
import { FormsModule } from '@angular/forms';
import { CarouselModule } from 'ngx-owl-carousel-o';


@NgModule({
  declarations: [
    SearchArticlesComponent
  ],
  imports: [
    CommonModule,
    CarouselModule,
    NgbPaginationModule,
    FormsModule,
    SearchArticlesRoutingModule
  ]
})
export class SearchArticlesModule { }
