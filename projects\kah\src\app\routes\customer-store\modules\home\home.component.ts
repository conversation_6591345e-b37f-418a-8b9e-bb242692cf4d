import { Component, OnInit } from '@angular/core';
import { HomeService } from '../../services/home.service';

@Component({
  selector: 'app-home',
  templateUrl: './home.component.html',
  styleUrls: ['./home.component.scss']
})
export class HomeComponent implements OnInit {

  public homeData: any[] = [];
  constructor(public service: HomeService) { }

  ngOnInit(): void {
    this.getHomePageDetails();
  }

  getHomePageDetails() {
    this.service.getHomePageDetails().subscribe({
      next: (res: any) => {
        this.homeData = res?.data?.attributes?.Body || [];
      }
    });
  }
}
