import { Injectable } from '@angular/core';
import {
  HttpInterceptor,
  HttpRequest,
  HttpHandler,
  HttpErrorResponse,
  HttpResponse,
} from '@angular/common/http';

import { catchError, Observable, of, tap, throwError } from 'rxjs';
import { AuthService } from 'ng-snjya';
import { Router } from '@angular/router';
import { environment } from '../../../environments/environment';

@Injectable()
export class AuthInterceptor implements HttpInterceptor {
  constructor(private authService: AuthService, private router: Router) {}

  intercept(req: HttpRequest<any>, next: HttpHandler) {
    if (this.authService.isLoggedIn) {
      const authToken = this.authService.getToken();
      if (req.url.includes(environment.apiEndpoint)) {
        req = req.clone({
          setHeaders: {
            Authorization: 'Bearer ' + authToken,
          },
        });
      }
    }
    return next
      .handle(req)
      .pipe(
        tap((event: any) => {
          if (event instanceof HttpResponse) {
            const newToken = event.headers.get('refreshtoken');
            const auth = this.authService.getAuth();
            if (newToken && auth) {
              auth.token = newToken;
              this.authService.setAuth(auth);
            }
          }
        })
      )
      .pipe(catchError((x) => this.handleAuthError(x)));
  }

  private handleAuthError(err: HttpErrorResponse): Observable<any> {
    if (err.status === 401 || err.status === 403) {
      this.authService.removeAuthToken();

      // Save the current frontend route
      const currentUrl = this.router.url;
      const endpoints: any[] = [
        environment.apiEndpoint,
        environment.cmsEndpoint,
      ];
      if (!endpoints.includes(err.url)) {
        localStorage.setItem('redirectUrl', currentUrl);
      }

      // Redirect to login page
      this.router.navigate(['/auth/login']).then(() => {
        window.location.reload();
      });

      return of(err.message);
    }
    return throwError(() => err);
  }
}
