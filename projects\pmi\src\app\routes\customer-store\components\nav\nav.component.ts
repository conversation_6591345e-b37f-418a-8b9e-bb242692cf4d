import { Component, HostListener, Input } from '@angular/core';
import { HomeService } from '../../services/home.service';
import { StoreFrontAPIConstant } from '../../constants/api.constants';
import { Router } from '@angular/router';
import { AuthService } from 'ng-snjya';
import { environment } from '../../../../../environments/environment'

declare var $: any;

@Component({
  selector: 'app-nav',
  templateUrl: './nav.component.html',
  styleUrls: ['./nav.component.scss']
})

export class NavComponent {

  mmb_btn: boolean = false;
  cmsUrl = `/admin/auth/login`;
  clickEvent() {
    this.mmb_btn = !this.mmb_btn;
  }

  logout() {
    this.authService.doLogout();
  }

  links: any = [];
  private _data!: any;
  @Input() set data(data: any) {
    if (data) {
      this.setPermissions(data);
    }
  }
  get data() {
    return this._data;
  }

  imgPath: string = StoreFrontAPIConstant.IMG_URL;

  public mainmenuData: any[] = [];
  constructor(
    public Homeservice: HomeService,
    private router: Router,
    private authService: AuthService,
  ) { }

  getCmsRoles(): any[] {
    const cmsInfo = localStorage.getItem('userInfo');
    if (cmsInfo) {
      try {
        const info = JSON.parse(cmsInfo);
        if (info?.userRoles?.length) {
          return info.userRoles.map((role: any) => role.name);
        }
      } catch (error) {
        console.log(error);
      }
    }
    return [];
  }

  setPermissions(data: any) {
    const role: any = this.authService.userDetail.code;
    const isAdmin = ['SUPER_ADMIN', 'ADMIN', 'EMPLOYEE_ADMIN'].includes(role);
    const cmsRoles = this.getCmsRoles();
    const accessibleUserRoles = ["ACCOUNT"];
    const cmsPermittedRoles = ['Content_Creator_PMI', 'Site_Manager_PMI'];
    this._data = data;
    const urls: any = [];
    for (let i = 0; i < this.data.Header_User_Details.length; i++) {
      const element = this.data.Header_User_Details[i];
      if (isAdmin) {
        urls.push(element);
        continue;
      }
      switch (element.header_user_url) {
        case `${environment.cmsEndpoint}/admin/auth/login`:
          if (['EMPLOYEE_CMS', 'EMPLOYEE_ADMIN'].includes(role) || cmsPermittedRoles.some(permission => cmsRoles.includes(permission))) {
            urls.push(element);
          }
          break;
        case "/#/store/my-account?t=register-user":
        case "/#/store/my-account?t=account-overview":
          if (accessibleUserRoles.indexOf(role) != -1) {
            urls.push(element);
          }
          break;
        case "/#/store/my-account?t=my-profile":
        case "/#/store/my-account?t=favorite":
          urls.push(element);
          break;
      }
    }
    this.links = urls;
  }

  ngOnInit(): void {
    this.getMainmenuDetails();
  }

  get isHomePage(): boolean {
    return this.router.url.indexOf('home') !== -1;
  }

  getMainmenuDetails() {
    this.Homeservice.getMainmenuDetails().subscribe({
      next: (res: any) => {
        this.mainmenuData = res;
      }
    });
  }

}
