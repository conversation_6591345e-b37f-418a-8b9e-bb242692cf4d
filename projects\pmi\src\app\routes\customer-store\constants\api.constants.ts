import { environment } from "../../../../environments/environment";
export const StoreFrontAPIConstant = {
    IMG_URL: `https://snjy-strapi-upload.s3.us-east-2.amazonaws.com/`,
    CMS_ME: `${environment.cmsEndpoint}/admin/users/me`,
    HOME_API_DETAILS: `${environment.cmsEndpoint}/api/home-page`,
    MAIN_MENU_API_DETAILS: `${environment.cmsEndpoint}/api/navigation/render/pmi?type=TREE`,
    Categories: `${environment.cmsEndpoint}/api/categories`,
    Categories_Page: `${environment.cmsEndpoint}/api/category-page-pmi`,
    Articles: `${environment.cmsEndpoint}/api/articles`,
    PMI_LOGIN_API_DETAILS: `${environment.cmsEndpoint}/api/login-page-pmi`,
    Favorite_articles: `${environment.apiEndpoint}/favorite-articles`,
    Activation: `${environment.apiEndpoint}/activation-requests`,
    CreateMarketingTicket: `${environment.apiEndpoint}/marketing-tickets/create`,
    ListMarketingTicket: `${environment.apiEndpoint}/marketing-tickets/list`,
    DetailMarketingTicket: `${environment.apiEndpoint}/marketing-tickets/detail`,
    Time: `${environment.apiEndpoint}/send-times`,
    DMs: `${environment.apiEndpoint}/authorized-dmas`,
    Activation_Offers: `${environment.apiEndpoint}/activation-offers`,
    TICKET_STATUSES: `${environment?.apiEndpoint}/ticket-statuses`,
    CampaignCenterPage: `${environment.cmsEndpoint}/api/campaign-center`,
    GetSignedUrl: `${environment.apiEndpoint}/signed-url`
}