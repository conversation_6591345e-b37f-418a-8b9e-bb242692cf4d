.banner-sec {
    margin: 0;
    padding: 0;
    height: 240px;
    background-size: cover !important;

    .banner-body {
        margin: 0 auto;
        padding: 0 30px;
        max-width: 1500px;
        height: 100%;
        align-items: center;
        justify-content: center;
        gap: 16px;

        h1 {
            margin: 40px 0 0 0;
            padding: 0;
            position: relative;
            display: flex;
            font-family: var(--brand-font-family-secondary);
            font-weight: var(--brand-font-weight-bold);
            color: var(--brand-color-deep-nevy-blue);
        }

        .tab-buttons {
            margin: 20px 0 0 0;
            position: relative;
            gap: 0 10px;

            .tab-btn {
                margin: 0;
                padding: 0;
                display: flex;
                align-items: center;
                justify-content: center;
                width: 220px;
                height: 46px;
                font-family: var(--brand-font-family-secondary);
                font-weight: var(--brand-font-weight-bold);
                background: var(--brand-color-white);
                border-radius: 50px;
                cursor: pointer;
                border: none;

                &.selected {
                    color: var(--brand-color-white);
                    background: var(--brand-color-primary);
                }

                &:hover {
                    opacity: 0.8;
                }
            }
        }
    }

    &:before {
        position: absolute;
        content: '';
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background: #00000024;
    }
}

.bedcrumbs-sec {
    padding: 20px 0;
    background: var(--brand-color-main-background-secondary);

    .bedcrumbs {
        margin: 0 auto !important;
        max-width: 1500px;
        padding: 0 30px;
        font-family: var(--brand-font-family-secondary);
        font-weight: var(--brand-font-weight-medium);
        font-size: var(--papa-m-font-size-0-875);
        color: var(--papa-m-color-dark-secondary);
        line-height: 12px;
    }
}

.campaign-sec {
    margin: 0;
    padding: 20px 0 40px 0;
    position: relative;
    background: var(--brand-color-main-background-secondary);
    min-height: 100vh;

    .campaign-body {
        gap: 30px;
        margin: 0 0 30px 0;

        .campaign-left {
            margin: 0;
            padding: 0;
            position: relative;
            width: 60%;
            height: 100%;

            h3 {
                margin: 0 0 15px 0;
                padding: 0;
                font-family: var(--brand-font-family-secondary);
                color: var(--brand-color-primary);
                font-weight: var(--brand-font-weight-bold);
            }

            p {
                margin: 0 0 16px 0;
                padding: 0;
                position: relative;
                font-family: var(--brand-font-family-secondary);
                font-size: 16px;
                color: var(--brand-color-deep-nevy-blue);
            }

            ul {
                margin: 30px 0 0 0;
                padding: 30px 0 0 0;
                display: flex;
                flex-direction: column;
                gap: 8px 0;
                border-top: 1px solid var(--brand-color-light-border);

                li {
                    margin: 0;
                    padding: 0;
                    font-family: var(--brand-font-family-secondary);
                    line-height: 18px;
                }
            }
        }

        .campaign-right {
            margin: 0;
            padding: 0;
            position: relative;
            width: 40%;
            height: 100%;
            margin: 0;
            padding: 20px;
            position: relative;
            width: 40%;
            background: var(--brand-color-white);
            border: 1px solid var(--brand-offwhite);
            border-radius: 12px;
            box-shadow: 0 2px 4px #0000000d;

            h3 {
                margin: 0 0 22px 0;
                padding: 0 0 16px 0;
                font-family: var(--brand-font-family-secondary);
                color: var(--brand-color-deep-nevy-blue);
                font-weight: var(--brand-font-weight-bold);
                font-size: 18px;
                border-bottom: 1px solid var(--brand-color-light-border);
            }
        }
    }
}

.campaign-left-form {
    margin: 40px 0 0 0;

    form {
        position: relative;
        gap: 0 16px;

        .form-group {
            margin: 0;
            position: relative;
            width: 50%;

            label {
                margin: 0 0 8px 0;
                padding: 0;
                display: flex;
                font-family: var(--brand-font-family-secondary);
                font-size: var(--brand-font-size-1);
                font-weight: var(--brand-font-weight-semi-bold);
                line-height: 18px;
            }

            .form-control {
                margin: 0;
                padding: 0 12px;
                height: 48px;
                background: var(--brand-color-white);
                border-radius: 8px;
                border: 1px solid var(--brand-accordian-button-color) !important;
                font-family: var(--brand-font-family-secondary);
                font-size: var(--brand-font-size-0-9);
                font-weight: var(--brand-font-weight-medium);
                box-shadow: none;
                appearance: auto !important;
            }
        }
    }
}

.campaign-right {
    form {
        overflow: auto;
        height: 600px;
        gap: 18px 0;
        padding: 0 16px 0 0;

        &::-webkit-scrollbar {
            width: 7px;
            height: 5px;
        }

        &::-webkit-scrollbar-track {
            background: var(--brand-color-white);
            border: 1px solid var(--brand-color-light-border);
            border-radius: 50px;
        }

        &::-webkit-scrollbar-thumb {
            background: var(--brand-color-light-border);
            border-radius: 50px;
        }

        .form-lavel {
            position: relative;
            margin: 0;
            display: flex;
            justify-content: start;
            font-size: 16px;
            color: var(--brand-color-dark-600);
            gap: 0 8px;
            cursor: pointer;

            input {
                &.form-control {
                    display: none;
                }
            }

            .check-box {
                margin: 0;
                padding: 0;
                position: relative;
                display: flex;
                align-items: center;
                justify-content: center;
                width: 22px;
                height: 22px;
                background: var(--brand-color-white);
                border: 2px solid var(--brand-accordian-button-color);
                border-radius: 5px;
                transition: all 0.3s ease-in-out;

                span {
                    font-size: 19px;
                    font-weight: 600;
                    color: #FFF;
                }
            }

            &:has(input:checked) .check-box {
                background: var(--brand-color-primary);
                border: 2px solid var(--brand-color-primary);
            }

            &.radio-form-label {
                padding: 12px;
                border: 2px solid var(--brand-scroll-thumb-hover-color);
                border-radius: 8px;
                box-shadow: 0 2px 2px #00000014;
                font-size: 15px;
                line-height: 22px;

                &:has(input:checked) {
                    border: 2px solid var(--brand-color-primary);
                }
            }
        }
    }

    &.full-width {
        .campaign-left-form {
            margin: 20px 0;
        }

        form {
            gap: 0 40px;
            height: fit-content;
        }
    }
}

.campaign-box-list {
    margin: 50px 0 0 0;
    padding: 24px;
    width: 100%;
    background: var(--brand-color-white);
    border: 1px solid var(--brand-offwhite);
    border-radius: 12px;
    box-shadow: 0 2px 4px #0000000d;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 40px;

    .campaign-box {
        width: 100%;

        h3 {
            margin: 0 0 5px 0 !important;
            padding: 0;
            font-family: var(--brand-font-family-secondary);
            font-size: 18px;
            color: var(--brand-color-deep-nevy-blue) !important;
            line-height: 17px;
            display: flex;
            align-items: center;
            gap: 0 8px;

            span {
                color: var(--brand-color-secondary-accent) !important;
                position: relative;
                top: -1px;
            }
        }

        p {
            margin: 0 0 8px 0 !important;
            padding: 0 0 0 30px !important;
            font-family: var(--brand-font-family-primary) !important;
            font-size: 15px !important;
            color: var(--brand-color-dark-600) !important;
        }

        h4 {
            margin: 0 0 10px 0 !important;
            padding: 0;
            font-family: var(--brand-font-family-secondary);
            font-size: 18px;
            color: var(--brand-color-primary);
            font-weight: var(--brand-font-weight-bold);
            line-height: 20px;
        }
    }
}

.campaign-submit-body {
    margin: 50px auto;
    padding: 0 30px;
    max-width: 1500px;
    gap: 0 20px;

    .submit-btn {
        margin: 0;
        padding: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 190px;
        height: 48px;
        font-family: var(--brand-font-family-secondary);
        font-weight: var(--brand-font-weight-bold);
        background: var(--brand-color-primary);
        color: var(--brand-color-white);
        border-radius: 50px;
        cursor: pointer;
        gap: 0 6px;
        border: none;
        &:disabled {
            opacity: .6;
            cursor: not-allowed;
        }

        &.prev {
            color: var(--brand-color-white);
            background: var(--brand-color-black);
        }
    }
}

.full-width {
    width: 100% !important;
}

.step {
    display: none;
    margin: 0 auto 40px auto;
    padding: 0 30px;
    max-width: 1500px;

    &.active {
        display: block;
    }
}