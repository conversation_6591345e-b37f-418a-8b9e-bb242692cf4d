<footer class="footer-sec">
    <div class="footer-body">
        <div class="footer-top d-flex flex-column">
            <div class="ftr-top-logo d-flex align-items-center justify-content-between">
                <div class="footer-logo ftr-logo-box"></div>
                <div class="footer-logo ftr-logo-box d-flex justify-content-center">
                    <a routerLink="/">
                        <img [src]="footerData?.footer_logo?.data?.attributes?.url" class="img-fluid" alt="" />
                    </a>
                </div>
                <div class="back-top ftr-logo-box d-flex justify-content-end">
                    <a href="javascript:void(0)" (click)="backToTop()">Back to top <span class="material-symbols-outlined">keyboard_arrow_up</span></a>
                </div>
            </div>
            <div class="ftr-social-media-list d-flex align-items-center justify-content-center">
                <ul class="d-flex align-items-center justify-content-center">
                    <ng-container *ngFor="let footerSocialLinks of footerData?.Footer_Social_Media_Links">
                        <li *ngIf="footerSocialLinks?.social_media_link">
                            <a [href]="[footerSocialLinks?.social_media_link]" target="_blank">
                                <img [src]="footerSocialLinks?.Footer_social_media_img?.data?.attributes?.url" alt="" /> {{footerSocialLinks?.social_media_text}}
                            </a>
                        </li>
                    </ng-container>
                </ul>
            </div>
            <div class="ftr-top-links d-flex justify-content-between" *ngIf="footerLinksdata?.footer_c_links">
                <ng-container>
                    <div class="ftr-top-link-box d-flex justify-content-between">
                        <div class="ftr-top-link-title">{{footerLinksdata?.footer_link_title}}</div>
                        <ul>
                            <ng-container *ngFor="let footerLinks of footerLinksdata?.footer_c_links">
                                <li *ngIf="footerLinks?.footer_link">
                                    <a [routerLink]="footerLinks?.footer_link">{{footerLinks?.footer_link_name}}</a>
                                </li>
                            </ng-container>
                        </ul>
                    </div>
                </ng-container>
            </div>
        </div>
        <div class="footer-line"></div>
        <div class="footer-bottom">
            <div class="ftr-copy-right d-flex align-items-center">{{footerData?.footer_copyright}}</div>
            <div class="ftr-bottom-links">
                <ul>
                    <ng-container *ngFor="let bottomFooterLinks of footerData?.Bottom_Footer_Links">
                        <li *ngIf="bottomFooterLinks?.footer_link">
                            <a [routerLink]="[bottomFooterLinks?.footer_link]">{{bottomFooterLinks?.footer_link_name}}</a>
                        </li>
                    </ng-container>
                </ul>
            </div>
        </div>
    </div>
</footer>