import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TearmsAndConditionsRoutingModule } from './tearms-and-conditions-routing.module';
import { TearmsAndConditionsComponent } from './tearms-and-conditions.component';
import { SharedModule } from 'projects/kah/src/app/shared/shared.module';


@NgModule({
  declarations: [
    TearmsAndConditionsComponent
  ],
  imports: [
    CommonModule,
    SharedModule,
    TearmsAndConditionsRoutingModule
  ]
})
export class TearmsAndConditionsModule { }
