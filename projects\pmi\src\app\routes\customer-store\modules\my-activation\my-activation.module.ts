import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { MyActivationRoutingModule } from './my-activation-routing.module';
import { MyActivationComponent } from './my-activation.component';
import { NgbDatepickerModule, NgbPaginationModule, NgbTypeaheadModule } from '@ng-bootstrap/ng-bootstrap';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { CarouselModule } from 'ngx-owl-carousel-o';
import { AgGridModule } from 'ag-grid-angular';
import { MyActivationDetailsComponent } from './my-activation-details/my-activation-details.component';
import { GridComponent } from './grid/grid.component';
import { GridCustomHeaderComponent } from './grid-custom-header/grid-custom-header.component';

@NgModule({
    declarations: [
        MyActivationComponent,
        MyActivationDetailsComponent,
        GridComponent,
        GridCustomHeaderComponent
    ],
    imports: [
        CommonModule,
        CarouselModule,
        NgbPaginationModule,
        NgbDatepickerModule,
        NgbTypeaheadModule,
        ReactiveFormsModule,
        FormsModule,
        AgGridModule,
        MyActivationRoutingModule
    ],
})
export class MyActivationModule { }