<section class="home-banner-sec">
    <div class="home-banner-img">
        <owl-carousel-o [options]="customOptions">
            <ng-container *ngFor="let slide of data?.banner_imgs">
                <ng-template carouselSlide [id]="slide.id?.toString()">
                    <div class="home-b-img" style="background: url('{{slide.banner_imgs.data.attributes.url}}') center center no-repeat;" (click)="openUrl(slide)"></div>
                </ng-template>
            </ng-container>
        </owl-carousel-o>
    </div>
    <div class="home-banner-body">
        <h1>{{data?.Banner_Title}}</h1>
        <ng-container *ngIf="data?.Banner_Title">
            <div class="h-banner-search-form">
                <form>
                    <div class="form-group">
                        <input type="search" class="form-control" placeholder="Search for anything...">
                    </div>
                    <div class="h-banner-search-btn d-flex align-items-center">
                        <button type="button" class="btn btn-primary">
                            <span class="material-symbols-outlined">{{data?.Banner_search_icon}}</span>
                            {{data?.Banner_search_text}}</button>
                    </div>
                </form>
            </div>
        </ng-container>
    </div>
</section>