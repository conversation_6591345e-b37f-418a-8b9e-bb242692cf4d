.navbar-brand {
    width: 150px;
}

.copy-right {
    font-size: var(--brand-font-size-0-875);
    color: var(--brand-color-dark-600);
    font-weight: var(--brand-font-weight-semi-bold);
}

.nav-link {
    li {
        a {
            font-size: var(--brand-font-size-0-875);
            color: var(--brand-color-dark-600);
            font-weight: var(--brand-font-weight-semi-bold);
            text-transform: capitalize;

            &:hover {
                text-decoration: underline !important;
            }
        }
    }
}

.forgot-password-box {
    margin: 0 auto;
    max-width: 475px;
    width: 100%;
}

h4 {
    margin: 0 0 30px 0;
    font-weight: var(--brand-font-weight-semi-bold);
    font-family: var(--brand-font-family-primary);
    color: var(--brand-color-primary);
    font-size: 2rem;
    line-height: 42px;
}

.form-container {
    border-radius: 12px;
    border: 1px solid var(--brand-accordian-button-color);
    background: var(--brand-color-white);
    box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.09);
}

form {
    border-radius: 12px;
    background: var(--brand-color-white);
}

label {
    margin: 0 0 8px 0;
    padding: 0;
    display: flex;
    align-items: center;
    font-size: var(--brand-font-size-0-875);
    color: var(--brand-color-dark-600);
    font-weight: var(--brand-font-weight-semi-bold);
    line-height: 13px;
    text-transform: uppercase;
}

input {
    margin: 0;
    padding: 0 15px;
    height: 48px;
    font-size: var(--brand-font-size-0-875);
    font-weight: var(--brand-font-weight-semi-bold);
    background: var(--brand-color-white);
    border-radius: 8px;
    border: 1px solid var(--brand-accordian-button-color) !important;

    &:hover {
        border: 1px solid var(--brand-border-color);
        outline: none;
        box-shadow: none;
    }
}

.hint {
    margin: 15px 0 0 0;
    padding: 0;
    display: block;
    font-size: var(--brand-font-size-1);
    font-weight: var(--brand-font-weight-semi-bold);
    color: #6c757d;
    font-family: var(--brand-font-family-primary);
}

.button-section {
    display: flex;
    gap: 0 12px;
    cursor: pointer;

    button {
        margin: 0;
        padding: 0;
        position: relative;
        height: 52px;
        display: flex;
        justify-content: center;
        align-items: center;
        font-family: var(--brand-font-family-primary);
        font-size: var(--brand-font-size-1);
        color: var(--brand-color-white);
        font-weight: var(--brand-font-weight-normal);
        line-height: 14px;
        text-transform: uppercase;
        background: var(--brand-color-primary);
        transition: all0 0.3s ease-in-out;
        border: none;
        border-radius: 10px;
    }

    button.btn.btn-primary {
        background: transparent !important;
        border: 1px solid var(--brand-color-dark-secondary) !important;
        color: var(--brand-color-dark-secondary) !important;
    }
}

@media (max-width: 500px) {
    form {
        min-width: auto;
    }

    button {
        padding: .5rem 1rem;
        font-size: var(--brand-font-size-0-875);
    }
}