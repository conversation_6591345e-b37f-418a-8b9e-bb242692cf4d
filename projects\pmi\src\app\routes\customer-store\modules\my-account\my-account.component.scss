:host {
    display: block;
}

.profile-and-fav-banner-sec {
    margin: 0;
    padding: 30px 0 0 0;

    .profile-and-fav-banner-vody {
        margin: 0 auto;
        padding: 0 30px;
        max-width: 1500px;
        height: 100%;
        gap: 12px 0;

        .profile-and-fav-title {
            margin: 0;
            padding: 0;
            font-size: var(--brand-font-size-3);
            font-weight: var(--brand-font-weight-semi-bold);
            color: var(--brand-color-secondary-neutral-charcoal);
        }
    }
}

.profile-and-fav-sec {
    margin: 0;
    padding: 30px 0 50px 0;

    .profile-and-fav-body {
        margin: 0 auto;
        padding: 0 30px;
        max-width: 1500px;
        width: 100%;
        gap: 0 3%;

        .pf-sidebar {
            margin: 0;
            padding: 20px;
            position: sticky;
            width: 24%;
            height: 600px;
            border-radius: 12px;
            background: var(--brand-color-white);
            border: 1px solid var(--brand-border-color);
            box-shadow: 0 3px 6px rgba(0, 0, 0, 0.03);

            .pv-tab-list {
                margin: 0;
                padding: 0;
                gap: 5px 0;
                width: 100%;

                a {
                    margin: 0;
                    padding: 0 10px;
                    position: relative;
                    display: flex;
                    align-items: center;
                    height: 48px;
                    gap: 0 8px;
                    font-size: var(--brand-font-size-1);
                    font-weight: var(--brand-font-weight-normal);
                    color: var(--brand-color-deep-nevy-blue);
                    background: var(--brand-color-white);
                    border-radius: 8px;

                    &:hover {
                        background: var(--brand-border-color);
                    }

                    &.active {
                        color: var(--brand-table-first-column-color);
                        background: var(--brand-color-light-cyan);
                    }
                }
            }
        }

        .pf-body {
            margin: 0;
            padding: 0;
            gap: 24px;
            width: 73%;

            .pf-top-sec {
                margin: 0;
                padding: 0;

                .profile-img-sec {
                    margin: 0;
                    padding: 0;
                    width: 100%;
                    height: fit-content;
                    gap: 0 20px;

                    .profile-img {
                        margin: 0;
                        padding: 0;
                        width: 80px;
                        height: 80px;
                        overflow: hidden;
                        border-radius: 100px;

                        img {
                            position: relative;
                            width: 100%;
                            height: 100%;
                            object-fit: cover;
                        }
                    }

                    .profile-title {
                        margin: 0 0 15px 0;
                        padding: 0;
                        font-size: var(--brand-font-size-1-25);
                        font-weight: var(--brand-font-weight-semi-bold);
                        color: var(--brand-color-deep-nevy-blue);
                        line-height: 16px;
                    }
                }

                .pf-logout-btn button {
                    margin: 0;
                    padding: 0;
                    width: 140px;
                    height: 40px;
                    font-size: var(--brand-font-size-0-875);
                    font-weight: var(--brand-font-weight-medium);
                    gap: 0 8px;
                    background: var(--brand-color-white);
                    border: 1px solid var(--brand-accordian-button-color);
                    border-radius: 8px;
                    color: var(--brand-color-secondary-neutral-charcoal);
                    cursor: pointer;

                    span {
                        font-size: var(--brand-font-size-1-25);
                    }

                    &:hover {
                        color: var(--brand-table-first-column-color);
                        border: 1px solid var(--brand-table-first-column-color);
                    }
                }
            }

            .pf-line {
                width: 100%;
                min-height: 1px;
                background: var(--brand-accordian-button-color);
            }

            .profile-content-sec {
                gap: 24px 0;

                form {
                    width: 100%;
                    gap: 24px 3%;
                    flex-wrap: wrap;

                    .form-group {
                        position: relative;
                        display: flex;
                        flex-direction: column;
                        gap: 10px 0;
                        width: 48%;

                        &.full-width {
                            width: 100% !important;
                        }

                        label {
                            position: relative;
                            font-size: var(--brand-font-size-0-9);
                            font-weight: var(--brand-font-weight-semi-bold);
                            color: var(--brand-color-dark-600);
                            line-height: 14px;
                        }

                        .form-control {
                            margin: 0;
                            padding: 0 14px;
                            position: relative;
                            font-size: var(--brand-font-size-1);
                            color: var(--brand-color-secondary-neutral-charcoal);
                            height: 48px;
                            border-radius: 8px;
                            background: var(--brand-color-white);
                            border: 1px solid var(--brand-color-light-border) !important;
                            appearance: auto !important;
                            box-shadow: none;

                            &::placeholder {
                                color: var(--brand-color-tertiary-gray);
                            }
                        }

                        .change-password {
                            font-size: var(--brand-font-size-1);
                            font-weight: var(--brand-font-weight-semi-bold);
                            color: var(--brand-table-first-column-color);
                            line-height: 12px;
                            text-decoration: underline !important;
                            cursor: pointer;
                        }
                    }

                    .save-btn-sec {
                        margin: 35px 0 0 0;
                        gap: 0 20px;

                        button {
                            width: fit-content;
                            padding: 0 16px;
                            height: 48px;
                            font-size: var(--brand-font-size-1);
                            font-weight: var(--brand-font-weight-normal);
                            color: var(--brand-color-white);
                            background: var(--brand-color-primary);
                            border: none;
                            border-radius: 8px;
                            gap: 0 8px;
                            cursor: pointer;
                            transition: all 0.3s ease-in-out;

                            &.cancel-btn {
                                width: 160px;
                                font-size: var(--brand-font-size-1);
                                font-weight: var(--brand-font-weight-normal);
                                color: var(--brand-color-deep-nevy-blue);
                                background: var(--brand-color-white);
                                border: 1px solid var(--brand-accordian-button-color);
                                gap: 0 8px;
                            }
                        }
                    }

                }
            }

        }

    }
}

.error-message {
    color: red;
    font-size: 12px;
    margin-top: 4px;
}

/*----ALL TABLE STYLE----*/


/*----MY FAVORITE STYLE----*/

.my-f-top-sec {
    margin: 0 0 40px 0;

    .my-favorite-title {
        font-size: var(--brand-font-size-2);
        font-weight: var(--brand-font-weight-medium);
        color: var(--brand-color-secondary-neutral-charcoal);
    }

    .my-favorite-search {
        width: 40%;

        .form-control {
            margin: 0;
            padding: 0 14px;
            position: relative;
            font-size: 16px;
            color: var(--brand-color-deep-nevy-blue);
            height: 48px;
            border-radius: 8px;
            background: var(--brand-color-white);
            border: 1px solid var(--brand-color-light-border) !important;
            appearance: auto !important;

            &::placeholder {
                color: #c9c9c9;
            }
        }

        .f-search-btn {
            position: absolute;
            top: 0;
            bottom: 0;
            right: 10px;
            margin: auto;
            height: fit-content;
            width: fit-content;
            background: none;
            box-shadow: none;
            border: none;
            display: flex;
            align-items: center;
            justify-content: center;

            span {
                color: var(--brand-color-primary);
                font-weight: var(--brand-font-weight-medium);
            }
        }
    }
}

.my-favorite-table-sec {
    table {
        border-collapse: separate;
        border-spacing: 0;

        thead {
            tr {
                th {
                    margin: 0;
                    padding: 0 12px;
                    position: relative;
                    height: 62px;
                    vertical-align: middle;
                    line-height: 20px;
                    letter-spacing: 0.25px;
                    border: 1px solid var(--brand-accordian-button-color);
                    border-left: none;
                    border-right: none;
                    font-size: 15px;
                    font-weight: 500;
                    color: var(--brand-color-secondary-neutral-charcoal);
                    border-bottom: 1px solid var(--brand-color-secondary-accent);

                    .th-text {
                        gap: 0 8px;

                        span {
                            font-size: 22px;
                        }
                    }

                    &:last-child {
                        .th-text {
                            margin: 0 0 0 auto;
                            justify-content: flex-end;
                        }
                    }
                }
            }
        }

        tbody {
            tr {
                td {
                    margin: 0;
                    padding: 0 12px;
                    position: relative;
                    height: 62px;
                    vertical-align: middle;
                    line-height: 20px;
                    border: none;
                    font-size: 15px;
                    color: var(--brand-color-dark-600) !important;
                    letter-spacing: 0.25px;
                    white-space: nowrap;

                    .article-link {
                        margin: 0 0 0 auto;
                        padding: 0;
                        position: relative;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        width: 110px;
                        height: 32px;
                        font-size: 14px;
                        gap: 0 8px;
                        background: var(--brand-color-primary);
                        border-radius: 6px;
                        color: var(--brand-color-white);
                        cursor: pointer;
                    }

                    .td-text {
                        gap: 0 8px;

                        span {
                            font-size: 22px;
                        }
                    }
                }
            }

            .border-bottom-success td {
                border-bottom: 1px solid #b8d5ae !important;
            }

            .border-bottom-info td {
                border-bottom: 1px solid #abdfef !important;
            }

            .border-bottom-warning td {
                border-bottom: 1px solid #ffd698 !important;
            }

            .border-bottom-danger td {
                border-bottom: 1px solid #ffbcb8 !important;
            }

            .border-bottom-light td {
                border-bottom: 1px solid var(--brand-color-light-border) !important;
            }
        }
    }
}

/*----ALL TABLE STYLE----*/


/*----ACCOUNT OVERVIEW STYLE----*/
.acc-overview-title {
    margin: 0 0 15px 0;
    padding: 0;
    font-size: var(--brand-font-size-1-25);
    font-weight: var(--brand-font-weight-semi-bold);
    color: var(--brand-color-deep-nevy-blue);
    line-height: 16px;
}

.acc-details-box {
    margin: 0;
    padding: 0;
    position: relative;
    display: flex;
    flex-direction: column;
    gap: 16px 0;
    background: var(--brand-scroll-track-color);
    border-radius: 8px;

    .acc-details-title {
        margin: 0;
        padding: 16px;
        position: relative;
        height: 60px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        border-bottom: 1px solid var(--brand-color-light-border);

        &>span {
            margin: 0;
            padding: 0;
            font-size: var(--brand-font-size-1-25);
            font-weight: var(--brand-font-weight-semi-bold);
            color: var(--brand-color-tertiary-dark-teal);
            line-height: 16px;
        }

        .acc-details-box-arrow {
            margin: 0;
            padding: 0;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 28px;
            height: 28px;
            background: #e8e9ed;
            border: none;
            border-radius: 50px;
            transition: all 0.3s ease-in-out;
            transform: rotate(0deg);
            cursor: pointer;
        }

        .acc-details-box-arrow.open {
            transform: rotate(180deg) !important;
        }
    }

    .acc-details-lists {
        margin: 0;
        padding: 16px;
        position: relative;
        display: grid;
        gap: 24px;
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));

        .acc-details-text {
            margin: 0;
            padding: 0;
            position: relative;
            display: flex;
            flex-direction: column;
            gap: 6px;
            font-size: 14px;
            line-height: 18px;
            color: var(--brand-color-secondary-neutral-charcoal);

            span {
                margin: 0;
                padding: 0;
                display: flex;
                font-size: 13px;
                line-height: 13px;
                color: var(--brand-color-dark-600);
            }

            .val {
                line-height: 22px;
            }

        }
    }

    .acc-details-table {
        margin: 0 auto 16px auto;
        padding: 16px;
        max-width: calc(100% - 32px);
        width: 100%;
        background: var(--brand-color-white);
        border-radius: 8px;
        overflow: hidden;
        display: none;
        flex-direction: column;
        gap: 16px;

        .acc-details-table-title {
            margin: 0;
            padding: 10px 0 10px 0;
            font-size: var(--brand-font-size-1-25);
            font-weight: var(--brand-font-weight-semi-bold);
            color: var(--brand-color-tertiary-dark-teal);
            line-height: 16px;
        }

        .table {
            margin: 0;
            padding: 0 10px 10px 0;
            position: relative;
            overflow: auto;

            table {
                min-width: 100%;
            }

            thead {
                tr {
                    th {
                        margin: 0;
                        padding: 0 12px;
                        position: relative;
                        height: 48px;
                        vertical-align: middle;
                        line-height: 20px;
                        letter-spacing: 0.25px;
                        border: 1px solid var(--brand-accordian-button-color);
                        border-left: none;
                        border-right: none;
                        font-size: 14px;
                        font-weight: 400;
                        color: var(--brand-color-secondary-neutral-charcoal);
                        border-bottom: 1px solid #f3c8ba;
                        white-space: nowrap;
                    }
                }
            }

            tbody {
                tr {
                    td {
                        margin: 0;
                        padding: 0 12px;
                        position: relative;
                        height: 48px;
                        vertical-align: middle;
                        line-height: 20px;
                        letter-spacing: 0.25px;
                        border: 1px solid var(--brand-accordian-button-color);
                        border-left: none;
                        border-right: none;
                        font-size: 14px;
                        font-weight: 400;
                        color: var(--brand-color-dark-600);
                        border-bottom: 1px solid #f3c8ba;
                        white-space: nowrap;

                        // .reset-p {
                        //     margin: 0;
                        //     padding: 3px 12px;
                        //     position: relative;
                        //     display: flex;
                        //     align-items: center;
                        //     justify-content: center;
                        //     font-size: 13px;
                        //     background: var(--brand-footer-bg);
                        //     text-transform: capitalize;
                        //     color: var(--brand-color-primary);
                        //     border: 1px solid #ffe5e5;
                        //     border-radius: 50px;
                        // }

                        .status-label {
                            position: relative;
                            cursor: pointer;

                            input[type=checkbox] {
                                position: absolute;
                                left: 7px;
                                top: 0;
                                bottom: 0;
                                margin: auto;
                                accent-color: black;
                                width: 10px;
                                z-index: 1;
                            }

                            span {
                                margin: 0;
                                padding: 0 5px 0 20px;
                                position: relative;
                                width: 80px;
                                height: 28px;
                                display: none;
                                align-items: center;
                                background: var(--brand-color-white);
                                color: #000;
                                border-radius: 50px;
                                font-size: 13px;
                                line-height: 13px;
                            }

                            input[type=checkbox]:checked~.active-s {
                                display: flex;
                                background: #00968829;
                                color: #009688;
                            }

                            input[type=checkbox]~.inactive-s {
                                display: flex;
                                background: #ffe5e5;
                                color: var(--brand-color-primary);
                            }

                            input[type=checkbox]:checked~.inactive-s {
                                display: none;
                            }
                        }

                        .edit-form {
                            color: var(--brand-color-dark-600);
                            min-width: 125px;
                            max-width: 100%;
                            width: 100%;
                        }

                        .dropdown-table-btn {
                            margin: 0 0 0 auto;
                            padding: 0;
                            position: relative;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            width: 24px;
                            height: 24px;
                            background: var(--brand-color-light-border);
                            border: none;
                            border-radius: 100px;
                            transition: all 0.3s ease-in-out;
                            transform: rotate(0deg);
                            cursor: pointer;

                            span {
                                font-size: 18px;
                            }
                        }

                        .dropdown-table-btn.close {
                            transform: rotate(180deg);
                        }
                    }
                }
            }
        }
    }

    .acc-details-table.show {
        display: flex !important;
    }
}

.edit-field {
    display: flex;
    align-items: center;
    gap: 0 4px;

    .edit-form {
        padding: 2px;
        font-size: 14px;
        line-height: 18px;
        color: var(--brand-color-secondary-neutral-charcoal);
        background: none;
        outline: 1px solid;
        border-radius: 3px;
        padding: 2px;
    }
}

.action-container {
    position: absolute;
    right: 12px;
    top: -5px;
    display: flex;
    gap: 5px;

    .edit-form-btn {
        margin: 0;
        padding: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 24px;
        height: 24px;
        background: var(--brand-color-white);
        border: none;
        border-radius: 100px;
        transition: all 0.3s ease-in-out;
        transform: rotate(0deg);
        cursor: pointer;

        span {
            font-size: 18px !important;
        }
    }
}

.dropdown-table {
    display: none;
    background: var(--brand-scroll-track-color);

    td {
        padding: 20px !important;

        .table-data-list {
            grid-template-columns: repeat(auto-fill, minmax(210px, 1fr));
            gap: 30px;
            min-height: 80px;

            .table-data-box {
                margin: 0;
                padding: 0;
                position: relative;
                display: flex;
                flex-direction: column;
                height: fit-content;
                gap: 6px;
                font-size: 14px;
                line-height: 18px;
                color: var(--brand-color-secondary-neutral-charcoal);

                span {
                    margin: 0;
                    padding: 0;
                    display: flex;
                    color: var(--brand-color-dark-600);
                }

                .reset-p {
                    margin: 0;
                    padding: 6px 20px;
                    position: relative;
                    display: flex;
                    width: fit-content;
                    align-items: center;
                    justify-content: center;
                    font-size: 13px;
                    background: var(--brand-color-white);
                    text-transform: capitalize;
                    color: var(--brand-color-primary);
                    border: none;
                    border-radius: 50px;
                    box-shadow: 0 2px 4px #********;
                }
            }
        }
    }
}

.dropdown-table.show {
    display: table-row;
}


/*----ACCOUNT OVERVIEW STYLE----*/

/*-------MEDIA SCREEN 1440px-------*/
@media only screen and (max-width: 1440px) {
    .profile-and-fav-banner-sec {
        .profile-and-fav-banner-vody {
            max-width: 1200px;
        }
    }

    .profile-and-fav-sec {
        .profile-and-fav-body {
            max-width: 1200px;
        }
    }
}

/*-------MEDIA SCREEN 1440px-------*/

/*-------MEDIA SCREEN 768px-------*/
@media only screen and (max-width: 768px) {
    .profile-and-fav-sec {
        .profile-and-fav-body {
            flex-direction: column;
            gap: 30px 0;

            .pf-sidebar {
                padding: 10px;
                height: fit-content;
                position: relative;
                width: 100%;

                .pv-tab-list {
                    padding: 0 0 10px 0;
                    gap: 6px;
                    flex-direction: row !important;
                    overflow-x: auto;

                    a {
                        min-width: fit-content;
                    }
                }
            }

            .pf-body {
                gap: 20px;
                width: 100%;

                .my-f-top-sec {
                    margin: 0 0 20px 0;
                }
            }
        }
    }
}

/*-------MEDIA SCREEN 768px-------*/

/*-------MEDIA SCREEN 576px-------*/
@media only screen and (max-width: 576px) {
    .profile-and-fav-banner-sec {
        .profile-and-fav-banner-vody {
            align-items: center;

            .profile-and-fav-title {
                font-size: var(--brand-font-size-2);
            }

            .bedcrumbs {
                font-size: var(--brand-font-size-0-8);
            }
        }
    }

    .profile-and-fav-sec {
        .profile-and-fav-body {
            .pf-sidebar {
                .pv-tab-list {

                    a {
                        font-size: var(--brand-font-size-0-875);

                        span {
                            font-size: 22px;
                        }
                    }
                }
            }

            .pf-body {
                .profile-content-sec {
                    form {
                        .save-btn-sec {
                            button {
                                width: 170px;
                                height: 42px;
                                font-size: var(--brand-font-size-0-875);
                            }
                        }
                    }
                }
            }
        }
    }
}

/*-------MEDIA SCREEN 576px-------*/

/*-------MEDIA SCREEN 576px-------*/
@media only screen and (max-width: 576px) {

    .profile-and-fav-sec {
        .profile-and-fav-body {
            .pf-body {
                .pf-top-sec {
                    .pf-logout-btn {
                        button {
                            width: 40px;
                            height: 40px;
                            font-size: 0;
                            gap: 0;
                            color: var(--brand-color-primary);
                        }
                    }
                }

                .profile-content-sec {
                    form {
                        .form-group {
                            width: 100%;
                        }

                        .save-btn-sec {
                            gap: 16px;
                            flex-direction: column;
                            width: 100%;

                            button {
                                width: 100% !important;
                            }
                        }
                    }
                }
            }
        }
    }

}

.form-check-input {
    border: 1px solid #ccc !important;

    &.is-invalid {
        border: 1px solid #dc3545 !important;
    }
}

/*-------MEDIA SCREEN 576px-------*/
.ng-select.custom ::ng-deep {

    .ng-select-container {
        min-height: 48px;
        border: 1px solid var(--brand-color-light-border) !important;
    }

    .ng-placeholder {
        top: 12px !important
    }
}