import { Component, Inject, OnInit } from '@angular/core';
import { ColDef } from 'ag-grid-community';
import { Subject, takeUntil } from 'rxjs';
import moment from 'moment';
import { NgbCalendar, NgbDateStruct } from '@ng-bootstrap/ng-bootstrap';
import { FormGroup, FormControl, Validators, ValidatorFn } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { AuthService, ToastService } from 'ng-snjya';
import { ROUTER_URL } from '../../../ng-snjya.config.token';
import { TicketsService } from './my-activation.service';

@Component({
  selector: 'pmi-my-activation',
  templateUrl: './my-activation.component.html',
  styleUrls: ['./my-activation.component.scss']
})
export class MyActivationComponent implements OnInit {
  private ngUnsubscribe = new Subject<void>();
  public moment: any = moment;
  public statuses: any = [];
  public tickets: any = [];
  public sellerDetails: any = {};
  public loading = false;
  public submitted = false;
  public filterForm!: FormGroup;
  public columnDefs: ColDef[] = [
    {
      field: 'TICKET_ID',
      headerName: 'Ticket ID',
      headerComponentParams: { menuIcon: 'pin' },
      cellRenderer: (data: any) => {
        return `<span class="d-flex align-items-center gap-1 grid-cell"><i class="material-icons-outlined">pin</i>${data.data.TICKET_ID}</span>`;
      },
      comparator: (valueA, valueB, nodeA, nodeB, isDescending) =>
        parseInt(valueA) - parseInt(valueB),
      sortable: true,
      resizable: true,
      maxWidth: 150
    },
    {
      field: 'SUBJECT',
      headerName: 'Subject',
      headerComponentParams: { menuIcon: 'fact_check' },
      cellRenderer: (data: any) => {
        return `<span class="d-flex align-items-center gap-1 grid-cell"><i class="material-icons-outlined">fact_check</i>${data.data.SUBJECT}</span>`;
      },
      resizable: true,
    },
    {
      field: 'PRIORITY_TEXT',
      headerName: 'Priority',
      headerComponentParams: { menuIcon: 'checklist_rtl' },
      cellRenderer: (data: any) => {
        return `<span class="d-flex align-items-center gap-1 grid-cell"><i class="material-icons-outlined">checklist_rtl</i>${data.data.PRIORITY_TEXT}</span>`;
      },
      resizable: true,
      maxWidth: 200
    },
    {
      field: 'STATUS_TEXT',
      headerName: 'Status',
      headerComponentParams: { menuIcon: 'done_all' },
      cellRenderer: (data: any) => {
        return `<span class="d-flex align-items-center gap-1 grid-cell"><i class="material-icons-outlined">done_all</i>${data.data.STATUS_TEXT}</span>`;
      },
      resizable: true,
      maxWidth: 200
    },
    {
      field: 'REPORTED_ON',
      headerName: 'Reported On',
      headerComponentParams: { menuIcon: 'radio_button_checked' },
      cellRenderer: (data: any) => {
        return `<span class="d-flex align-items-center gap-1 grid-cell"><i class="material-icons-outlined">radio_button_checked</i>${moment(
          data.data.REPORTED_ON
        ).format('MM/DD/YYYY')}</span>`;
      },
      sortable: true,
      resizable: true,
      maxWidth: 200
    },
  ];

  constructor(
    private ticketsService: TicketsService,
    public router: Router,
    private calendarService: NgbCalendar,
    @Inject(ROUTER_URL) public router_url: any,
    public authService: AuthService,
    private _snackBar: ToastService,
    private route: ActivatedRoute
  ) {
    this.sellerDetails = {
      ...this.getUserDetails(this.authService.userDetail),
    };
  }

  ngOnInit(): void {
    this.createForm();
    this.getAllTicketStatus();
  }

  createForm() {
    this.filterForm = new FormGroup(
      {
        DATE_FROM: new FormControl(null),
        DATE_TO: new FormControl(null),
        STATUS: new FormControl(''),
        TICKET: new FormControl(''),
        TICKET_TYPE: new FormControl('SRRQ'),
        SOLDTO: new FormControl(this.sellerDetails.customerId),
        CONTACTID: new FormControl(this.sellerDetails.contactId),
        SUBTYPE: new FormControl('188'),
        COUNT: new FormControl('100'),
      },
      [Validators.required, this.dateRangeValidator]
    );
  }

  getUserDetails(userDetail: any) {
    const obj = {
      customerId: '',
      contactId: ''
    }
    const ud = userDetail?.business_partners[0];
    if (ud) {
      obj.customerId = ud.bp_id;
      if (ud.bp_contacts_map?.length) {
        obj.contactId = ud.bp_contacts_map[0].contact_id;
      }
    }
    return obj;
  }

  clearFormField(name: string) {
    const obj: any = {};
    obj[name] = '';
    this.filterForm.patchValue(obj);
  }

  // convenience getter for easy access to form fields
  get f(): any {
    return this.filterForm?.controls;
  }

  private dateRangeValidator: ValidatorFn = (): {
    [key: string]: any;
  } | null => {
    let invalid = false;
    const from = this.filterForm && this.filterForm.get('DATE_FROM')?.value;
    const to = this.filterForm && this.filterForm.get('DATE_TO')?.value;
    if ((from && !to) || (!from && to)) {
      invalid = true;
    } else if (from && to) {
      invalid = new Date(from).valueOf() > new Date(to).valueOf();
    }
    return invalid ? { invalidRange: { from, to } } : null;
  };

  getAllTicketStatus() {
    this.ticketsService
      .getAllTicketStatus()
      .pipe(takeUntil(this.ngUnsubscribe))
      .subscribe({
        next: (res) => {
          this.statuses = res?.data || [];
          const statuses = this.statuses.map((val: any) => val.code).join(';');
          this.statuses.unshift({ code: statuses, description: 'All' });
          this.filterForm.patchValue({ STATUS: statuses });
          this.getTicketHistory();
        },
        error: () => {
          this._snackBar.open('Error while processing your request.', {
            type: 'Error',
          });
        },
      });
  }

  search() {
    this.getTicketHistory();
  }

  getTicketHistory() {
    this.submitted = true;
    if (this.filterForm.invalid) {
      return;
    }
    const payload: any = this.filterForm.value;
    payload.DATE_FROM = this.formatSearchDate(payload.DATE_FROM);
    payload.DATE_TO = this.formatSearchDate(payload.DATE_TO);
    this.loading = true;
    return this.ticketsService
      .getAll(payload)
      .pipe(takeUntil(this.ngUnsubscribe))
      .subscribe({
        next: (res) => {
          this.loading = false;
          this.submitted = false;
          this.tickets = res?.data?.SERVICEREQUESTS || [];
        },
        error: () => {
          this.loading = false;
          this.submitted = false;
          this._snackBar.open('Error while processing your request.', {
            type: 'Error',
          });
        },
      });
  }

  goToTicket(ticket: any) {
    this.router.navigate([
      `${ticket[0].TICKET_ID}`,
    ], { relativeTo: this.route });
  }

  clear() {
    this.filterForm.patchValue({ DATE_FROM: null });
    this.filterForm.patchValue({ DATE_TO: null });
    this.filterForm.patchValue({ TICKET: '' });
    const status = this.statuses.find((val: any) => val.description === 'All');
    this.filterForm.patchValue({ STATUS: status.code });
  }

  formatSearchDate(date: NgbDateStruct) {
    if (!date) return '';
    let newDate = new Date(date['year'], date['month'] - 1, date['day']);
    return moment(newDate).format('YYYYMMDD');
  }

  today() {
    return this.calendarService.getToday();
  }

  formatData(quotes: any) {
    return quotes.map(
      ({
        TICKET_ID,
        SUBJECT,
        STATUS_TEXT,
        PRIORITY_TEXT,
        REPORTED_ON,
        ASSIGNED_TO,
      }: {
        TICKET_ID: string;
        SUBJECT: string;
        STATUS_TEXT: string;
        PRIORITY_TEXT: string;
        REPORTED_ON: string;
        ASSIGNED_TO: string;
      }) => ({
        'Ticket ID': TICKET_ID,
        Subject: SUBJECT,
        Status: STATUS_TEXT,
        Priority: PRIORITY_TEXT,
        'Reported On': moment(REPORTED_ON).format('MM/DD/YYYY'),
        'Assigned To': ASSIGNED_TO,
      })
    );
  }

  ngOnDestroy() {
    this.ngUnsubscribe.next();
    this.ngUnsubscribe.complete();
  }
}
