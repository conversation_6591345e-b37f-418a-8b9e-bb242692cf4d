header.header {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 90px;
    z-index: 99;
    display: flex;
    align-items: center;
    background: transparent;
    transition: all 0.3s ease-in-out;

    .header-body {
        margin: 0 auto;
        padding: 0 30px;
        max-width: 1500px;
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: 0 45px;

        .main-logo {
            position: relative;
            max-width: 170px;
            width: 100%;
        }

        .main-menu {
            &>ul {
                margin: 0;
                position: relative;
                display: flex;
                align-items: center;
                gap: 0 20px;

                &>li {
                    position: relative;
                    padding: 28px 0;

                    &>a {
                        position: relative;
                        font-family: var(--brand-font-family-primary);
                        font-size: var(--brand-font-size-1);
                        color: var(--brand-color-white);
                        font-weight: var(--brand-font-weight-normal);
                        line-height: 12px;
                        display: flex;
                        align-items: center;
                        text-transform: capitalize;

                        span {
                            transition: all 0.3s ease-in-out;
                            font-size: var(--brand-font-size-22);
                            position: relative;
                            top: 2px;
                        }

                    }

                    .m-dropdown-arrow {
                        font-size: 24px;
                        background: transparent;
                        color: var(--brand-color-dark-600);
                        display: none !important;
                    }

                    .sub-menu {
                        margin: 0;
                        padding: 12px 0;
                        position: absolute;
                        background: var(--brand-color-white);
                        top: 110%;
                        left: 0;
                        box-shadow: 0 4px 4px rgb(0 0 0 / 10%);
                        min-width: 280px;
                        opacity: 0;
                        visibility: hidden;
                        transition: all 0.3s ease-in-out;
                        border-radius: 6px;

                        &>ul {
                            display: flex;
                            flex-direction: column;
                            gap: 0;
                            position: relative;
                            margin: 0;
                            padding: 0;

                            &>li {
                                padding: 0 10px;
                                position: relative;

                                &>a {
                                    position: relative;
                                    padding: 0 15px;
                                    display: flex;
                                    align-items: center;
                                    width: 100%;
                                    height: 40px;
                                    font-family: var(--brand-font-family-primary);
                                    font-size: var(--brand-font-size-0-9);
                                    color: var(--brand-color-dark-600);
                                    font-weight: var(--brand-font-weight-semi-bold);
                                    line-height: 18px;
                                    background: var(--bs-white);
                                    border-radius: 6px;
                                    transition: all 0.3s ease-in-out;
                                    white-space: nowrap;
                                    text-transform: capitalize;

                                    span {
                                        transition: all 0.3s ease-in-out;
                                        font-size: var(--brand-font-size-22);
                                        position: absolute;
                                        top: 0;
                                        right: 5px;
                                        bottom: 0;
                                        margin: auto;
                                        height: fit-content;
                                    }

                                    &:hover {
                                        color: var(--brand-color-primary);
                                        background: var(--brand-footer-bg);

                                        span {
                                            color: var(--brand-color-primary);
                                            transform: rotate(-90deg);
                                        }
                                    }
                                }

                                .sub-sub-menu {
                                    margin: 0;
                                    padding: 12px 0;
                                    position: absolute;
                                    background: var(--brand-color-main-background-secondary);
                                    top: 20px;
                                    left: 100%;
                                    box-shadow: 3px 4px 4px rgba(0, 0, 0, 0.1);
                                    min-width: 280px;
                                    opacity: 0;
                                    visibility: hidden;
                                    transition: all 0.3s ease-in-out;
                                    border-radius: 6px;

                                    &>ul {
                                        display: flex;
                                        flex-direction: column;
                                        gap: 0;
                                        position: relative;
                                        margin: 0;
                                        padding: 0;

                                        &>li {
                                            padding: 4px 10px;

                                            &>a {
                                                font-size: var(--brand-font-size-1);
                                                padding: 0 15px;
                                                font-weight: var(--brand-font-weight-normal);
                                                display: flex;
                                                align-items: center;
                                                width: 100%;
                                                height: 40px;
                                                font-family: var(--brand-font-family-primary);
                                                font-size: var(--brand-font-size-0-9);
                                                color: var(--brand-color-dark-600);
                                                font-weight: var(--brand-font-weight-semi-bold);
                                                transition: all 0.3s ease-in-out;
                                                background: var(--brand-color-main-background-secondary);
                                                border-radius: 6px;
                                                white-space: nowrap;
                                                text-transform: capitalize;

                                                &:hover {
                                                    color: var(--brand-color-primary);
                                                    background: var(--brand-footer-bg);
                                                }
                                            }
                                        }
                                    }
                                }

                                &:hover .sub-sub-menu {
                                    top: 5px;
                                    opacity: 1;
                                    visibility: visible;
                                }
                            }
                        }
                    }

                    &:hover .sub-menu {
                        top: 100%;
                        opacity: 1;
                        visibility: visible;
                    }

                    &:hover>a {

                        span {
                            transform: rotate(-180deg);
                        }
                    }
                }
            }
        }

        .h-right {
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: fit-content;
            margin: 0;
            gap: 0 20px;

            .h-notification {
                a {
                    margin: 0;
                    padding: 0;
                    position: relative;
                    display: flex;
                    color: var(--brand-color-dark-secondary);
                    align-items: center;
                    font-variation-settings: "wght" 400;

                    &:hover {
                        color: var(--brand-color-danger);
                    }
                }
            }

            .h-user {
                position: relative;

                button {
                    position: relative;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    line-height: 13px;
                    cursor: pointer;
                    border: none;
                    background: transparent;

                    &::after {
                        display: none;
                    }

                    .user-link-icon {
                        position: relative;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        height: 40px;
                        width: 40px;
                        color: var(--brand-color-white);
                        border: 2px solid var(--brand-color-white);
                        border-radius: 50px;
                        background: var(--brand-color-primary);
                    }

                    .arrow-down {
                        width: 20px;
                        height: 20px;
                        color: var(--brand-color-white);
                    }

                    &:hover {
                        color: var(--brand-color-danger);
                    }
                }

                .user-popup {
                    position: absolute !important;
                    inset: initial !important;
                    top: 70px !important;
                    right: 0 !important;
                    padding: 15px;
                    display: flex;
                    align-items: flex-start;
                    flex-direction: column;
                    gap: 4px;
                    width: 240px;
                    background: var(--brand-color-white);
                    border-radius: 8px;
                    box-shadow: 0 3px 4px rgba(0, 0, 0, 0.07);
                    opacity: 0;
                    visibility: hidden;
                    transition: all 0.3s ease-in-out;
                    transform: none !important;

                    a {
                        padding: 5px 5px;
                        display: flex;
                        align-items: center;
                        gap: 0 5px;
                        width: 100%;
                        height: 40px;
                        font-size: var(--brand-font-size-0-9);
                        color: var(--brand-color-dark-600);
                        transition: all 0.3s ease-in-out;
                        justify-content: start;
                        line-height: 13px;
                        cursor: pointer;

                        span {
                            font-size: var(--brand-font-size-22);
                        }

                        &:hover {
                            color: var(--brand-color-primary);
                        }
                    }

                    .line {
                        display: flex;
                        width: 100%;
                        min-height: 1px;
                        background: var(--brand-accordian-button-color);
                    }
                }
            }

            .h-user.show {
                .user-popup {
                    top: 59px !important;
                    opacity: 1;
                    visibility: visible;
                }
            }
        }

        .mobile-menu-btn-sec {
            margin: 0 0 0 auto;
            padding: 0;

            .mmb-btn {
                width: 36px;
                height: 36px;
                background: none;
                border: none;

                .mmb-menu-bars {
                    position: relative;
                    color: var(--brand-color-white);
                    display: none;
                }

                .mmb-menu-close {
                    position: relative;
                    color: var(--brand-color-white);
                    display: none;
                }
            }

            .mmb-btn.open {
                .mmb-menu-bars {
                    display: flex;
                }
            }

            .mmb-btn.close {
                .mmb-menu-close {
                    display: flex;
                }
            }
        }
    }
}

header.header.header_down {
    background: var(--bs-white);
    box-shadow: 3px 0 6px rgb(0 0 0 / 14%);
    height: 76px;

    .main-menu {
        &>ul {

            &>li {

                &>a {
                    color: var(--brand-color-black) !important;
                }
            }
        }
    }

    .mobile-menu-btn-sec {
        .mmb-btn {
            .mmb-menu-bars {
                color: var(--brand-color-black) !important;
            }

            .mmb-menu-close {
                color: var(--brand-color-black) !important;
            }
        }
    }

    .h-right {
        .h-user {
            button {
                .arrow-down {
                    color: var(--brand-color-black) !important;
                }
            }
        }
    }

}



/*-------MEDIA SCREEN 1440px-------*/
@media only screen and (max-width: 1440px) {
    header.header {
        .header-body {
            max-width: 1200px;
            gap: 0 15px;

            .main-logo {
                max-width: 140px;
            }

            .main-menu {
                &>ul {
                    gap: 0 12px;

                    &>li {
                        &>a {
                            font-size: var(--snjy-font-size-0-875);

                        }

                        .sub-menu {

                            &>ul {

                                &>li {

                                    &>a {
                                        height: 36px;
                                        font-size: var(--brand-font-size-0-875);

                                    }

                                    .sub-sub-menu {

                                        &>ul {
                                            &>li {

                                                &>a {
                                                    height: 36px;
                                                    font-size: var(--brand-font-size-0-875);
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }

                    }
                }
            }
        }
    }
}

/*-------MEDIA SCREEN 1440px-------*/

/*-------MEDIA SCREEN 1024px-------*/
@media only screen and (max-width: 1024px) {
    header.header {
        height: 72px;

        .header-body {
            .main-menu {
                position: absolute;
                top: 80px;
                left: 0;
                width: 100%;
                height: calc(100vh - 70px);
                padding: 15px 30px;
                background: var(--brand-color-white);
                visibility: hidden;
                opacity: 0;
                transition: all 0.3s ease-in-out;
                border-top: 1px solid var(--brand-color-light-border);
                box-shadow: 0 2px 5px var(--brand-color-light-border);
                overflow: auto !important;

                &>ul {
                    flex-direction: column;
                    align-items: start !important;

                    &>li {
                        width: 100%;
                        padding: 0 !important;
                        border-bottom: 1px solid var(--brand-color-main-background-secondary);

                        &:last-child {
                            border-bottom: none;
                        }

                        &>a {
                            justify-content: space-between;
                            height: 40px;
                            align-items: center;
                            color: var(--brand-color-black);

                            span {
                                display: none !important;
                            }
                        }

                        .m-dropdown-arrow {
                            margin: auto;
                            display: flex !important;
                            position: absolute;
                            top: 8px;
                            right: 0;
                            width: 24px;
                            height: 24px;
                            border: 1px solid var(--brand-accordian-button-color);

                            .down {
                                transform: rotate(0deg) !important;
                                transition: all 0.3s ease-in-out;
                            }

                            .up {
                                transform: rotate(-180deg) !important;
                                transition: all 0.3s ease-in-out;
                            }
                        }

                        .sub-menu {
                            position: relative !important;
                            opacity: 1;
                            visibility: visible;
                            border: none;
                            border-radius: 0;
                            box-shadow: none;
                            padding: 5px 10px;
                            background: var(--brand-color-main-background-secondary);
                            display: none;

                            &>ul {
                                &>li {
                                    padding: 0;
                                    position: relative;
                                    border-bottom: 1px solid var(--brand-accordian-button-color);

                                    &>a {
                                        height: 36px;
                                        font-size: var(--brand-font-size-0-875);
                                        padding: 0;
                                        background: var(--brand-color-main-background-secondary);
                                        font-weight: var(--brand-font-weight-normal);

                                        span {
                                            display: none !important;
                                        }
                                    }

                                    .sub-sub-menu {
                                        position: relative !important;
                                        left: 0;
                                        opacity: 1;
                                        visibility: visible;
                                        border: none;
                                        border-radius: 0;
                                        box-shadow: none;
                                        padding: 5px 10px;
                                        background: var(--brand-color-white);
                                        display: none;

                                        &>ul {
                                            &>li {
                                                padding: 0;
                                                position: relative;
                                                border-bottom: 1px solid var(--brand-color-main-background-secondary);

                                                &>a {
                                                    height: 36px;
                                                    font-size: var(--brand-font-size-0-875);
                                                    padding: 0;
                                                    background: var(--brand-color-white);
                                                    font-weight: var(--brand-font-weight-normal);

                                                    span {
                                                        display: none !important;
                                                    }
                                                }

                                                &:last-child {
                                                    border-bottom: none;
                                                }
                                            }
                                        }
                                    }

                                    .sub-sub-menu.show {
                                        display: block !important;
                                    }

                                    &:last-child {
                                        border-bottom: none !important;
                                    }
                                }
                            }
                        }

                        .sub-menu.show {
                            display: block !important;
                        }
                    }
                }
            }

            .main-menu.show {
                visibility: visible;
                opacity: 1;
                top: 72px;
            }

            .mobile-menu-btn-sec {
                display: flex !important;
            }
        }

    }

}

/*-------MEDIA SCREEN 1024px-------*/

/*-------MEDIA SCREEN 414px-------*/
@media only screen and (max-width: 414px) {
    header.header {
        .header-body {
            .main-logo {
                max-width: 110px;
            }
        }
    }
}

/*-------MEDIA SCREEN 414px-------*/