import { Component, Input } from '@angular/core';
import { StoreFrontAPIConstant } from '../../constants/api.constants';
import { OwlOptions } from 'ngx-owl-carousel-o';

@Component({
  selector: 'app-upcoming-news',
  templateUrl: './upcoming-news.component.html',
  styleUrls: ['./upcoming-news.component.scss']
})
export class UpcomingNewsComponent {

  @Input() data: any;

  imgPath: string = StoreFrontAPIConstant.IMG_URL;

  constructor() { }

  ngOnInit(): void { }

  newsOptions: OwlOptions = {
    loop: true,
    autoplay: true,
    dots: false,
    nav: false,
    navSpeed: 2500,
    autoplayTimeout: 2500,
    autoplaySpeed: 2500,
    animateOut: 'fadeOut',
    touchDrag: false,
    mouseDrag: false,
    responsive: {
      0: {
        items: 1,
      },
      600: {
        items: 1,
      },
      1000: {
        items: 1,
      },
    },
  };

}
