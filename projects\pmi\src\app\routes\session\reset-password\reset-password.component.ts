import { Component, Input } from '@angular/core';
import {
  AbstractControl,
  FormBuilder,
  FormGroup,
  ValidationErrors,
  ValidatorFn,
  Validators,
} from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';

import { ResetPasswordService } from './reset-password.service';
import { AuthService, ToastService } from 'ng-snjya';
import { HomeService } from '../../customer-store/services/home.service';
import { ComponentNameConstants } from '../../customer-store/constants/components.contants';

function ConfirmedValidator(controlName: string, matchingControlName: string) {
  return (formGroup: FormGroup) => {
    const control = formGroup.controls[controlName];
    const matchingControl = formGroup.controls[matchingControlName];
    if (
      matchingControl.errors &&
      !matchingControl.errors['confirmedValidator']
    ) {
      return;
    }
    if (control.value !== matchingControl.value) {
      matchingControl.setErrors({ confirmedValidator: true });
    } else {
      matchingControl.setErrors(null);
    }
  };
}

function patternValidator(regex: RegExp, error: ValidationErrors): ValidatorFn {
  return (control: AbstractControl) => {
    if (!control.value) {
      // if control is empty return no error
      return null;
    }

    // test the value of the control against the regexp supplied
    const valid = regex.test(control.value);

    // if true, return no error (no error), else return error passed in the second parameter
    return valid ? null : error;
  };
}

@Component({
  selector: 'snjya-reset-password',
  templateUrl: './reset-password.component.html',
  styleUrls: ['./reset-password.component.scss'],
})
export class ResetPasswordComponent {
  form: FormGroup = this.formBuilder.group(
    {
      password: [
        '',
        [
          Validators.required,
          Validators.minLength(8),
          // check whether the entered password has a number
          patternValidator(/\d/, {
            hasNumber: true,
          }),
          // check whether the entered password has upper case letter
          patternValidator(/[A-Z]/, {
            hasCapitalCase: true,
          }),
          // check whether the entered password has a lower case letter
          patternValidator(/[a-z]/, {
            hasSmallCase: true,
          }),
          // check whether the entered password has a special character
          patternValidator(/[ !@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/, {
            hasSpecialCharacters: true,
          }),
        ],
      ],
      passwordConfirm: ['', Validators.required],
    },
    {
      validators: ConfirmedValidator('password', 'passwordConfirm'),
    }
  );
  public submitted = false;
  public saving = false;
  public token = '';
  public id: any = null;

  constructor(
    private formBuilder: FormBuilder,
    private service: ResetPasswordService,
    private authService: AuthService,
    private route: ActivatedRoute,
    public router: Router,
    private _snackBar: ToastService,
    public Forgotservice: HomeService,
  ) { }

  ngOnInit(): void {
    this.route.queryParams.subscribe((params) => {
      this.token = params['token'];
      this.id = params['id'];
      this.fetchData();
    });
  }

  get f(): { [key: string]: AbstractControl } {
    return this.form.controls;
  }

  onSubmit(): void {
    this.submitted = true;

    if (this.form.invalid) {
      return;
    }

    if (!this.token || !this.id) {
      this._snackBar.open('Invalid Url.', {
        type: 'Error',
      });
      return;
    }

    this.saving = true;
    this.service
      .resetPassword({
        user_id: this.id,
        password: this.authService.encryptString(this.form.value.password),
        token: this.token,
      })
      .subscribe({
        complete: () => {
          this.onReset();
          this.saving = false;
          console.log('Password reset successfully!');
        },
        error: (err) => {
          this.saving = false;
          this._snackBar.open('Error while processing your request.', {
            type: 'Error',
          });
        },
      });
  }

  onReset(): void {
    this.submitted = false;
    this.form.reset();
    setTimeout(() => {
      this.router.navigate(['backoffice']);
    }, 2000);
  }





  @Input() footerData: any = {};

  loading = false;

  fetchData() {
    this.loading = true;
    this.Forgotservice.getHomePageDetails().subscribe({
      next: (res: any) => {
        this.footerData = this.Forgotservice.getDataByComponentName(res?.data?.attributes?.Body || [], ComponentNameConstants.Footer);
        console.log(res);
        this.loading = false;
      }
    });
  }






}
