import { Injectable } from '@angular/core';
import { AuthService } from 'ng-snjya';

@Injectable({
    providedIn: 'root'
})
export class AuthWatchService {

    constructor(private auth: AuthService) { this.watch(); }

    watch() {
        window.addEventListener('storage', (event) => {
            if (event.key === 'jwtToken') {
                const val = event.newValue;
                if (!val) {
                    this.auth.doLogout();
                }
            }
        });
    }

}
