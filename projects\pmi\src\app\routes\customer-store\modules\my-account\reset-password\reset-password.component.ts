import { Component, Input, OnInit } from '@angular/core';
import { FormGroup, FormBuilder, Validators, AbstractControl, ValidationErrors, ValidatorFn } from '@angular/forms';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { AuthService, ToastService, UsersService } from 'ng-snjya';


function ConfirmedValidator(controlName: string, matchingControlName: string) {
  return (formGroup: FormGroup) => {
    const control = formGroup.controls[controlName];
    const matchingControl = formGroup.controls[matchingControlName];
    if (
      matchingControl.errors &&
      !matchingControl.errors['confirmedValidator']
    ) {
      return;
    }
    if (control.value !== matchingControl.value) {
      matchingControl.setErrors({ confirmedValidator: true });
    } else {
      matchingControl.setErrors(null);
    }
  };
}


function patternValidator(regex: RegExp, error: ValidationErrors): ValidatorFn {
  return (control: AbstractControl) => {
    if (!control.value) {
      // if control is empty return no error
      return null;
    }

    // test the value of the control against the regexp supplied
    const valid = regex.test(control.value);

    // if true, return no error (no error), else return error passed in the second parameter
    return valid ? null : error;
  };
}

@Component({
  selector: 'pmi-reset-password',
  templateUrl: './reset-password.component.html',
  styleUrls: ['./reset-password.component.scss'],
})
export class RegisterUserComponent implements OnInit {
  saving = false;
  form!: FormGroup;
  @Input() data: any = {};
  isPasswordFormSubmitted = false;

  constructor(
    public activeModal: NgbActiveModal,
    private fb: FormBuilder,
    private userService: UsersService,
    private _snackBar: ToastService,
    private authService: AuthService
  ) { }

  ngOnInit(): void {
    this.form = this.fb.group({
      currentPassword: [
        '',
        [
          Validators.required,
          Validators.minLength(8),
          // check whether the entered password has a number
          patternValidator(/\d/, {
            hasNumber: true,
          }),
          // check whether the entered password has upper case letter
          patternValidator(/[A-Z]/, {
            hasCapitalCase: true,
          }),
          // check whether the entered password has a lower case letter
          patternValidator(/[a-z]/, {
            hasSmallCase: true,
          }),
          // check whether the entered password has a special character
          patternValidator(/[ !@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/, {
            hasSpecialCharacters: true,
          }),
        ],
      ],
      confirmPassword: [''],
    }, {
      validators: ConfirmedValidator('currentPassword', 'confirmPassword'),
    });
  }

  get f(): { [key: string]: AbstractControl } {
    return this.form.controls;
  }

  onSubmit() {
    this.isPasswordFormSubmitted = true;
    if (this.form.invalid) {
      return;
    }
    debugger;
    this.userService
      .updateUserPassword(this.data.id, {
        password: this.authService.encryptString(this.form.value.confirmPassword),
      })
      .subscribe({
        complete: () => {
          this.isPasswordFormSubmitted = false;
          this.activeModal.close();
          this._snackBar.open('Password Changed successfully!');
        },
        error: (err: any) => {
          this.isPasswordFormSubmitted = false;
          this._snackBar.open('Error while processing your request.', {
            type: 'Error',
          });
        },
      });
  }
}
