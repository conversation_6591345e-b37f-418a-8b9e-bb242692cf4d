import { HttpClient } from '@angular/common/http';
import { AfterViewInit, Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { StoreFrontAPIConstant } from '../../constants/api.constants';
import { ArticleService } from '../../services/article.service';
import { debounceTime, distinctUntilChanged, Observable, OperatorFunction, Subject, switchMap, tap } from 'rxjs';
import { OwlOptions } from 'ngx-owl-carousel-o';

@Component({
  selector: 'pmi-categories',
  templateUrl: './categories.component.html',
  styleUrls: ['./categories.component.scss']
})
export class CategoriesComponent implements OnInit {
  @ViewChild("searchText", { static: false }) searchText!: ElementRef;
  id: string = '';
  loading: boolean = false;
  categoryDetails: any = {};
  categoryBannerDetails: any = {};
  subCategories: any[] = [];
  selectedSubCategory: string = '';
  searchSubject = new Subject<string>();
  queryParams: any = {};
  navSpeed = 2000;
  loaded = false;
  constructor(
    private route: ActivatedRoute,
    private http: HttpClient,
    private articleService: ArticleService,
    private router: Router
  ) { }

  setNavSpeed(data: any) {
    this.navSpeed = data.Banner_Timer || this.navSpeed;
    this.customOptions = {
      ...this.customOptions,
      navSpeed: this.navSpeed,
      autoplayTimeout: this.navSpeed,
      autoplaySpeed: this.navSpeed,
    };
  }

  ngOnInit(): void {
    this.fetchBannerDetails();
    this.route.paramMap.subscribe(params => {
      const id = params.get('id');
      this.id = id || ''
      this.fetchCategories();
    });
    this.route.queryParams.subscribe((params: any) => {
      if (!this.loaded && params.p) {
        this.currentPage = parseInt(params.p);
      }
      if (params.t) {
        this.searchTerm = params.t;
        this.queryParams.t = params.t;
      }
      if (params.s) {
        this.selectedSubCategory = params.s;
        this.queryParams.s = params.s;
      }
      this.loaded = true;
    });
    this.searchSubject.pipe(debounceTime(500)).subscribe(searchTerm => { this.searchTerm = searchTerm; this.fetchArticles(); });
  }

  fetchBannerDetails() {
    const url = `${StoreFrontAPIConstant.Categories_Page}?populate=deep`;
    this.http.get(url).subscribe({
      next: (value: any) => {
        if (value?.data?.attributes) {
          this.setNavSpeed(value?.data?.attributes);
          if (value?.data?.attributes?.Banner_images?.data?.length) {
            this.categoryBannerDetails = value?.data?.attributes.Banner_images.data;
          }
        }
      },
    });
  }

  fetchCategories(): void {
    this.loading = true;
    const url = `${StoreFrontAPIConstant.Categories}?populate=deep,2&filters[slug][$eq]=${this.id}`;
    this.http.get(url).subscribe({
      next: (value: any) => {
        if (value?.data[0]?.id && value?.data[0]?.attributes) {
          this.categoryDetails = { ...value.data[0].attributes, id: value.data[0].id };
          if (value?.data[0]?.attributes?.sub_categories?.data?.length) {
            this.subCategories = value.data[0].attributes.sub_categories?.data;
          }
        }
        this.loading = false;
        setTimeout(() => {
          if (this.queryParams.t)
            this.searchText.nativeElement.value = this.queryParams.t;
        });
        this.fetchArticles();
      },
    });
  }

  selectSubCategory(id: string) {
    if (this.selectedSubCategory == id) {
      this.selectedSubCategory = '';
    } else {
      this.selectedSubCategory = id;
    }
    this.fetchArticles();
  }

  articles: any[] = [];
  totalCount: number = 0;
  pageSizeOptions: number[] = [9, 18, 36, 72];
  pageSize: number = 9;
  currentPage: number = 0;
  searchTerm: string = '';
  sort = 'updatedAt:desc';
  loadingArticles = false;
  view = 'grid';

  onValueSearch(target: HTMLInputElement) {
    this.searchSubject.next(target.value);
  }

  fetchArticles() {
    this.loadingArticles = true;
    const category = this.categoryDetails.id;
    let subCategories = this.subCategories.map(subCategory => (subCategory.id));
    if (this.selectedSubCategory) {
      const subCategoryDetail = this.subCategories.find((sub) => sub.attributes.slug == this.selectedSubCategory);
      if (subCategoryDetail) {
        subCategories = [subCategoryDetail.id];
      }
    }
    this.articleService.getArticles(this.currentPage, this.pageSize, this.searchTerm, this.sort, category, subCategories, !!this.selectedSubCategory)
      .subscribe(response => {
        this.articles = response.data;
        this.totalCount = response.meta.pagination.total;
        this.loadingArticles = false;
      });
  }

  onPageChange(event: any) {
    if (!event) return;
    this.router.navigate([], {
      relativeTo: this.route,
      queryParams: {
        p: this.currentPage === 0 ? null : this.currentPage,
      },
      queryParamsHandling: 'merge',
    });
    this.fetchArticles();
  }

  customOptions: OwlOptions = {
    loop: true,
    autoplay: true,
    dots: false,
    nav: false,
    navSpeed: this.navSpeed,
    autoplayTimeout: this.navSpeed,
    autoplaySpeed: this.navSpeed,
    animateOut: 'fadeOut',
    navText: ["<img src='/assets/images/arrow_back.svg' />", "<img src='/assets/images/arrow_forward.svg' />"],
    responsive: {
      0: {
        items: 1,
      },
      600: {
        items: 1,
      },
      1000: {
        items: 1,
      },
    },
  };
}
