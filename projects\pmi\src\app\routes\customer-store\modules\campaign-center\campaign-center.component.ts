import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import { CapmaignCenterService } from './campaign-center.service';
import { DomSanitizer } from '@angular/platform-browser';
import { AuthService, ToastService } from 'ng-snjya';
declare var $: any;

@Component({
  selector: 'pmi-campaign-center',
  templateUrl: './campaign-center.component.html',
  styleUrls: ['./campaign-center.component.scss']
})
export class CampaignCenterComponent implements OnInit {

  @ViewChild('scrollContainer', { static: false }) scrollContainer!: ElementRef;

  selectedStep = 1;
  loading = false;
  dms: any = [];
  sendTimes: any = [];
  offers: any = [];
  messageTypes: any = [];
  showCustomField = false;
  pageDetails: any = {};
  dataToSend: any = {
    useLocalTZ: false,
    sendDate: '',
    sendTime: '',
    markets: [],
    messageType: '',
    offerEndDate: '',
    offer: "",
    customMessage: ''
  }
  step1Desc!: any;
  step2MessageLayout!: any;
  step3Desc!: any;
  userDetails!: any;
  saving = false;
  minDate = this.formatDate();

  constructor(
    private service: CapmaignCenterService,
    private sanitizer: DomSanitizer,
    private _snackBar: ToastService,
    private authService: AuthService,
  ) { }

  ngOnInit(): void {
    this.userDetails = this.authService.userDetail;
    this.getData();
  }

  messageChanged(message: any) {
    if (message?.message_text?.type == 'CUSTOM') {
      this.toggleCustomField(true);
    } else {
      this.toggleCustomField(false);
    }
  }

  toggleCustomField(show: boolean) {
    this.showCustomField = show;
    this.dataToSend.customMessage = '';
  }

  formatDate() {
    const date = new Date();
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');

    return `${year}-${month}-${day}`;
  }

  getData() {
    this.loading = true;
    this.service.fetchAll().subscribe(data => {
      this.dms = data.dms;
      this.sendTimes = data.times;
      this.messageTypes = this.groupOffersByMessageType(data.offers);
      this.offers = data.offers;
      this.pageDetails = data.pageDetails;
      this.step1Desc = this.sanitizer.bypassSecurityTrustHtml(this.pageDetails.Step_1_Description);
      this.step2MessageLayout = this.sanitizer.bypassSecurityTrustHtml(this.pageDetails.Step_2_Message_Layout);
      this.step3Desc = this.sanitizer.bypassSecurityTrustHtml(this.pageDetails.Step_3_Description);
      this.loading = false;
    });
  }

  groupOffersByMessageType(offers: any) {
    const offerGroups = offers.reduce((acc: any, offer: any) => {
      const { message_type } = offer;
      if (!message_type?.description) {
        return acc;
      }
      if (!acc[message_type.description]) {
        acc[message_type.description] = {
          text: message_type.description,
          data: []
        };
      }
      acc[message_type.description].data.push(offer);
      return acc;
    }, {});
    const arr = [];
    for (const key in offerGroups) {
      if (Object.prototype.hasOwnProperty.call(offerGroups, key)) {
        const element = offerGroups[key];
        arr.push(element);
      }
    }
    return arr;
  }

  getMessages() {
    const mes = this.messageTypes.find((message: any) => message.text == this.dataToSend.messageType);
    return mes ? mes.data : [];
  }

  next() {
    if (this.selectedStep == 3) {
      this.submit();
      return;
    }
    this.selectedStep = ++this.selectedStep;
    this.scrollTop();
  }

  getUserDetails() {
    const obj = {
      customerId: '',
      contactId: ''
    }
    const userDetails = this.userDetails?.business_partners[0];
    if (userDetails) {
      obj.customerId = userDetails.bp_id;
      if (userDetails.bp_contacts_map?.length) {
        obj.contactId = userDetails.bp_contacts_map[0].contact_id;
      }
    }
    return obj;
  }

  submit() {
    const data = this.dataToSend;
    this.saving = true;
    const obj = {
      "team": "PCM",
      "assignedTo": "",
      "customerId": "",
      "contactId": "",
      "subType": "188",
      "sub_subType": "",
      "msgType": data.messageType,
      "selectedTextMsg": data.offer,
      "sendDate": data.sendDate,
      "sendTime": data.sendTime,
      "useLocalTZ": data.useLocalTZ ? 'Yes' : "No",
      "offer_endDate": data.offerEndDate,
      "targetMarkets": data.markets.join(','),
      "customMessage": data.customMessage,
      "requestor": this.userDetails?.display_name || '',
      "email": this.userDetails?.email || '',
    };
    if (this.userDetails?.business_partners?.length) {
      const userDetails = this.userDetails?.business_partners[0];
      if (userDetails) {
        obj.customerId = userDetails.bp_id;
        if (userDetails.bp_contacts_map?.length) {
          obj.contactId = userDetails.bp_contacts_map[0].contact_id;
        }
      }
    }
    this.service.createMarketingTicket(obj).subscribe(data => {
      this.saving = false;
      this.selectedStep = ++this.selectedStep;
      this.scrollTop();
      this._snackBar.open('Ticket Created successfully!');
    }, () => {
      this.saving = false;
      this._snackBar.open('Error while processing your request.', {
        type: 'Error',
      });
    });
  }

  prev() {
    this.selectedStep = --this.selectedStep;
    this.scrollTop();
  }

  scrollTop() {
    this.scrollContainer?.nativeElement?.scrollIntoView();
  }

  onMarket(event: any, data: any) {
    const checked = event.target.checked;
    const id: string = data?.authorized_market?.description || '';
    if (checked) {
      if (!this.dataToSend.markets.includes(id)) {
        this.dataToSend.markets.push(id);
      }
    } else {
      const index = this.dataToSend.markets.findIndex((data: any) => data == id)
      this.dataToSend.markets.splice(index, 1);
    }
  }

  isNextDisabled() {
    if (this.saving) {
      return true;
    }
    if (this.selectedStep == 1) {
      return !this.dataToSend.sendDate || !this.dataToSend.sendTime || !this.dataToSend.markets.length;
    }
    if (this.selectedStep == 2) {
      return !this.dataToSend.messageType || !this.dataToSend.offerEndDate || !this.dataToSend.offer;
    }
    return false;
  }

  createNew() {
    this.selectedStep = 1;
    this.dataToSend = {
      useLocalTZ: false,
      sendDate: '',
      sendTime: '',
      markets: [],
      messageType: '',
      offerEndDate: '',
      offer: '',
      customMessage: ''
    };
    this.showCustomField = false;
    this.scrollTop();
  }

  messageType(messageType: string) {
    const found = this.offers.find((message: any) => message.id == messageType);
    return found?.message_text || '';
  }

  getoffer(offer: string) {
    const found = this.offers.find((message: any) => message.id == offer);
    return found?.message_text || ''
  }

  getMarkets(markets: string[]) {
    const found: string[] = [];
    if (markets.length) {
      for (let i = 0; i < markets.length; i++) {
        const element = markets[i];
        const f = this.dms.find((dm: any) => dm.id == element);
        if (f) {
          found.push(f.authorized_market);
        }
      }
    }
    return found.join(', ')
  }

}
