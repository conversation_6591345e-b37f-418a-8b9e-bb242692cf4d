<div class="d-flex w-100 h-100 justify-content-center align-items-center" *ngIf="loading">
    <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">Loading...</span>
    </div>
</div>
<ng-container *ngIf="!loading">
    <app-nav [data]="headerData"></app-nav>
    <div class="flex-grow-1">
        <router-outlet></router-outlet>
    </div>
    <app-footer [footerLinksdata]="footerLinksdata" [footerData]="footerData"></app-footer>
</ng-container>