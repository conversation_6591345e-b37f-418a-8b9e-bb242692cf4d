.all-inner-banner-sec {
    margin: 0;
    padding: 0;
    overflow: hidden;
    z-index: 0;

    &:before {
        position: absolute;
        content: '';
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgb(0 0 0 / 20%);
        z-index: 10;
    }

    .category-slider {
        margin: 0;
        padding: 0;
        position: relative;

        .category-slider-img {
            margin: 0;
            padding: 0;
            position: relative;
            display: flex;
            width: 100%;
            height: 450px;
            background-size: cover !important;
        }
    }

    .all-inner-banner-body {
        margin: 0 auto;
        padding: 0 30px;
        max-width: 1500px;
        width: 100%;
        height: 100%;
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        z-index: 11;

        .all-inner-banner-title {
            font-size: var(--brand-font-size-5);
            font-weight: var(--brand-font-weight-bold);
            color: var(--brand-color-white);
            width: 100%;
            text-align: center;
            line-height: 48px;
        }

        .bedcrumbs {
            bottom: 24px;
            font-weight: var(--brand-font-weight-normal);
            font-size: var(--brand-font-size-0-875);
            color: var(--brand-color-white);
            line-height: 12px;
        }
    }
}

/*-------BANNER SEC-------*/


/*-------ARTICLE LISTING SEC-------*/
.article-listing-sec {
    padding: 50px 0;
    background: #F9F9FB;
    z-index: 0;

    .article-listing-body {
        margin: 0 auto;
        padding: 0 30px;
        max-width: 1500px;
        height: 100%;
        width: 100%;
        gap: 0 50px;

        .article-listing-left {
            width: 100%;
            height: 100%;
            gap: 48px 0;

            .article-listing-filter {
                gap: 0 14px;

                .view-tab {
                    padding: 8px;
                    width: 88px;
                    height: 48px;
                    background: var(--brand-color-white);
                    border: 1px solid var(--brand-color-light-border);
                    border-radius: 8px;
                    box-shadow: 0 6px 6px rgb(0 0 0 / 3%);
                    gap: 0 2px;

                    .view-btn {
                        width: 32px;
                        height: 32px;
                        border: none;
                        border-radius: 5px;
                        background: transparent;
                        color: var(--brand-color-dark-600);
                        cursor: pointer;

                        &:hover {
                            background: var(--brand-scroll-track-color);
                        }
                    }

                    .view-btn.active {
                        background: var(--brand-color-primary);
                        color: var(--brand-color-white);
                    }
                }

                .short-list {
                    max-width: 180px;
                    width: 100%;

                    label {
                        margin: auto;
                        top: 0;
                        bottom: 0;
                        left: 8px;
                        height: 24px;
                        width: 24px;

                        span {
                            font-size: var(--brand-font-size-1-25);
                            color: var(--brand-color-dark-600);
                        }
                    }

                    .form-control {
                        padding: 0 0 0 38px;
                        height: 48px !important;
                        background: var(--brand-color-white);
                        border: 1px solid var(--brand-color-light-border);
                        border-radius: 8px;
                        box-shadow: 0 6px 6px rgba(0, 0, 0, 0.03);
                        font-size: var(--brand-font-size-0-875);
                        color: var(--brand-color-dark-600);
                        appearance: auto;
                    }
                }

                .article-search-box {
                    max-width: 340px;
                    width: 100%;

                    .form-control {
                        padding: 0 14px 0 36px;
                        height: 48px !important;
                        background: var(--brand-color-white);
                        border: 1px solid var(--brand-color-light-border) !important;
                        border-radius: 8px;
                        box-shadow: 0 6px 6px rgba(0, 0, 0, 0.03) !important;
                        font-size: var(--brand-font-size-1);
                    }

                    label {
                        margin: auto;
                        top: 0;
                        bottom: 0;
                        left: 8px;
                        height: 24px;
                        width: 24px;
                        color: var(--brand-color-dark-600);
                    }
                }

                .show-post-list {
                    font-size: var(--brand-font-size-1);
                    color: var(--brand-color-dark-600);
                    margin: 0 0 0 auto;
                    gap: 0 8px;
                }

                .page-size {
                    gap: 0 8px;
                    font-size: var(--brand-font-size-1);
                    color: var(--brand-color-dark-600);

                    span {
                        display: flex;
                        min-width: fit-content;
                    }

                    .form-control {
                        padding: 0 20px 0 12px;
                        height: 48px !important;
                        background: var(--brand-color-white);
                        border: 1px solid var(--brand-color-light-border);
                        border-radius: 8px;
                        box-shadow: 0 6px 6px rgba(0, 0, 0, 0.03);
                        font-size: var(--brand-font-size-0-875);
                        color: var(--brand-color-dark-600);
                        appearance: auto;
                    }
                }

            }

            .article-listing {
                width: 100%;
                grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
                gap: 40px;

                .article-box {
                    width: 100%;
                    background: var(--brand-color-white);
                    border-radius: 12px;
                    box-shadow: 0 3px 4px rgb(0 0 0 / 6%);
                    overflow: hidden;

                    .article-box-img {
                        width: 100%;
                        height: 200px;
                        border-radius: 8px;
                        overflow: hidden;

                        &:before {
                            position: absolute;
                            content: '';
                            left: 0;
                            top: 0;
                            width: 100%;
                            height: 100%;
                            background: linear-gradient(180deg, #000000c9 4%, transparent 40%);
                        }

                        img {
                            height: 100%;
                            width: 100%;
                            object-fit: cover;
                        }

                        .article-features {
                            top: 0;
                            left: 0;
                            padding: 20px;
                            width: 100%;
                            z-index: 10;

                            .article-date,
                            .article-views {
                                font-size: var(--brand-font-size-0-875);
                                color: var(--brand-color-white);
                                line-height: 11px;
                                gap: 0 4px;

                                span {
                                    font-size: var(--brand-font-size-1-25);
                                }
                            }
                        }
                    }

                    .article-cnt {
                        gap: 20px 0;
                        padding: 20px;
                        flex-grow: 1;

                        .article-cnt-title {
                            padding: 0 0 10px 0;
                            font-size: var(--brand-font-size-26);
                            line-height: 34px;
                            color: var(--brand-color-secondary-neutral-charcoal);
                            font-weight: var(--brand-font-weight-bold);

                            &:before {
                                position: absolute;
                                content: '';
                                left: 0;
                                bottom: 0;
                                width: 80px;
                                height: 1px;
                                background: var(--brand-color-primary);
                            }
                        }

                        .article-cnt-p {
                            font-size: var(--brand-font-size-1);
                            color: var(--brand-color-dark-600);
                            line-height: 22px;
                            flex-grow: 1;
                        }

                        a {
                            margin: 20px 0 0 0;
                            padding: 14px;
                            width: 100%;
                            height: 52px;
                            font-size: var(--brand-font-size-1);
                            line-height: 17px;
                            color: var(--brand-color-white);
                            font-weight: var(--brand-font-weight-normal);
                            gap: 0 4px;
                            text-transform: uppercase;
                            background: var(--brand-color-secondary-accent);
                            justify-content: center;
                            border-radius: 8px;
                            transition: all 0.3s ease-in-out;

                            span {
                                font-size: var(--brand-font-size-1-25);
                            }
                        }
                    }

                    &:hover .article-cnt a {
                        background: var(--brand-color-primary);
                    }
                }
            }

            ngb-pagination {

                margin: 40px 0;

                ::ng-deep {
                    .pagination {
                        gap: 0 12px;
                    }

                    li {
                        &.active a {
                            color: var(--brand-color-white);
                            background-color: var(--brand-color-primary);
                            border: 1px solid var(--brand-color-primary);
                        }

                        &:hover:not(.active) a {
                            background-color: var(--brand-color-light-border);
                        }

                        a {
                            padding: 4px;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            width: 48px;
                            height: 48px;
                            font-size: var(--brand-font-size-1);
                            color: var(--brand-color-secondary-neutral-charcoal);
                            background-color: white;
                            border: 1px solid var(--brand-color-light-border);
                            border-radius: 6px;
                            cursor: pointer;
                            transition: all 0.3s ease-in-out;
                        }
                    }

                    .pagination-btn {
                        padding: 4px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        width: 48px;
                        height: 48px;
                        color: var(--brand-color-secondary-neutral-charcoal);
                        background: var(--brand-color-light-border);
                        border: 1px solid var(--brand-color-light-border);
                        border-radius: 6px;
                        transition: all 0.3s ease-in-out;

                        span {
                            font-size: 30px;
                        }
                    }
                }
            }
        }
    }
}

.h-200px {
    height: 200px;
}

/*-------MEDIA SCREEN 1440px-------*/
@media only screen and (max-width: 1440px) {
    .all-inner-banner-sec {
        .category-slider {
            .category-slider-img {
                height: 400px;
            }
        }

        .all-inner-banner-body {
            max-width: 1200px;

            .all-inner-banner-title {
                font-size: var(--brand-font-size-4);
            }
        }
    }

    .article-submenu {
        .article-submenu-body {
            max-width: 1200px !important;
        }
    }

    .article-listing-sec {
        .article-listing-body {
            max-width: 1200px;

            .article-listing-left {
                .article-listing {
                    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
                    gap: 30px;
                }
            }
        }
    }


}
/*-------MEDIA SCREEN 1440px-------*/

/*-------MEDIA SCREEN 1024px-------*/
@media only screen and (max-width: 1024px) {
    .all-inner-banner-sec {
        .category-slider {
            .category-slider-img {
                height: 320px;
            }
        }
    }


    .article-listing-sec {
        padding: 40px 0;

        .article-listing-body {
            .article-listing-left {
                .article-listing-filter {
                    flex-wrap: wrap;
                    gap: 14px;
                    justify-content: space-between;

                    .article-search-box {
                        order: 1;
                        max-width: 100% !important;
                    }

                    .view-tab {
                        order: 2;
                    }

                    .short-list {
                        order: 3;
                    }

                    .page-size {
                        order: 4;
                    }

                    .show-post-list {
                        order: 5;
                    }
                }

                .article-listing {
                    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
                    gap: 30px;

                    .article-box {
                        .article-cnt {
                            .article-cnt-title {
                                font-size: var(--brand-font-size-1-25);
                                line-height: 22px;
                                flex-grow: 1;
                            }

                            .article-cnt-p {
                                display: none;
                            }

                            a {
                                padding: 10px;
                                height: 40px;
                                font-size: var(--brand-font-size-0-875);
                                line-height: 17px;
                            }
                        }
                    }
                }
            }
        }
    }


}

/*-------MEDIA SCREEN 1024px-------*/


/*-------MEDIA SCREEN 576px-------*/
@media only screen and (max-width: 576px) {

    .article-submenu {
        height: 100px;

        .article-submenu-body {
            ul {
                overflow-x: auto;
                margin: 0 0 8px 0;
                gap: 0 10px;

                li {
                    min-width: fit-content;
                    height: 48px !important;
                    padding: 0 10px !important;
                    border: 1px solid var(--brand-border-color);
                    border-radius: 8px;

                    a {
                        flex-direction: row !important;
                        white-space: nowrap;
                        gap: 0 8px;
                    }
                }
            }
        }
    }

    .article-listing-sec {
        padding: 30px 0;

        .article-listing-body {
            .article-listing-left {
                gap: 30px 0;

                .article-listing-filter {
                    .page-size {
                        display: none !important;
                    }

                    .show-post-list {
                        display: none !important;
                    }
                }

                .article-listing {
                    .article-box {
                        .article-cnt {
                            .article-cnt-title {
                                font-size: var(--brand-font-size-1-125);
                            }
                        }
                    }
                }
            }
        }
    }
}

/*-------MEDIA SCREEN 576px-------*/