<div class="d-flex w-100 h-100 justify-content-center align-items-center" *ngIf="loading">
    <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">Loading...</span>
    </div>
</div>
<ng-container *ngIf="!loading">
    <section class="article-details-banner-sec position-relative">
        <div class="article-details-banner-body position-relative d-flex flex-column">
            <div class="bedcrumbs m-0 p-0 d-flex align-items-center">
                <span class="home-link cursor-pointer" routerLink="/store/home">Home</span>
                <span class="material-symbols-outlined">keyboard_arrow_right</span>
                <span class="home-link">{{ articleDetails.Heading }}</span>
            </div>
            <div class="banner-slider-sec" *ngIf="bannerImage">
                <img [src]="bannerImage" />
            </div>
        </div>
    </section>

    <section class="main-title-sec position-relative">
        <div class="main-title-body position-relative d-flex">
            <div class="main-title-left position-relative d-flex flex-column">
                <div class="main-title d-flex">{{ articleDetails.Heading }}</div>
                <div class="main-desc d-flex">{{ articleDetails.Short_Description }}</div>
                <div class="main-title-footer m-0 p-0 position-relative d-flex flex-column">
                    <div class="main-title-calendar d-flex align-items-center"><span
                            class="material-symbols-outlined">calendar_month</span> {{ articleDetails.updatedAt | date
                        }}</div>
                    <!-- <div class="category-list d-flex align-items-center">
                        <span class="category-tag">Category</span> :
                        <span class="category-name">Loyalty</span>
                        <span class="category-name">Marketing</span>
                        <span class="category-name">Brand Media Guidelines</span>
                        <span class="category-name">Planograms</span>
                    </div>
                    <div class="category-list d-flex align-items-center">
                        <span class="category-tag">Keywords</span> :
                        <span class="category-name">Logo</span>
                        <span class="category-name">Rewards</span>
                        <span class="category-name">Marketing</span>
                        <span class="category-name">Digital</span>
                        <span class="category-name">Vendor</span>
                    </div> -->
                </div>
            </div>
            <div class="main-title-right position-relative d-flex flex-column align-items-center">
                <div class="add-favorite-btn" *ngIf="!loadingFav">
                    <input type="checkbox" class="form-control" id="favorite-btn" [(ngModel)]="isFav"
                        (ngModelChange)="toggleFav()" />
                    <label class="d-flex align-items-center" for="favorite-btn"><span
                            class="material-symbols-outlined">favorite</span> {{ isFav ? 'Remove from Favorite' : 'Add to Favorite'}}</label>
                </div>
                <div class="latest-views d-flex justify-content-center align-items-center">
                    <span class="material-symbols-outlined">visibility</span> {{ views }} Views
                </div>
            </div>
        </div>
    </section>

    <section class="article-content-sec position-relative">
        <div class="article-content-body">
            <div class="article-content-title p-0 position-relative d-flex align-items-center">Article Content</div>
            <div class="article-content-details">
                <!-- <h2>{{ articleDetails.Heading }}</h2> -->
                <div class="article-details-html" [innerHtml]="articleBody"></div>

                <div class="article-media d-flex position-relative" *ngIf="videoUrl">
                    <div class="article-media-video">
                        <video controls>
                            <source [src]="videoUrl" [type]="videoMimeType">
                        </video>
                    </div>
                </div>
            </div>


            <div class="media-pdf position-relative d-grid" *ngIf="attachments.length">
                <a href="javascript: void(0)" class="media-pdf-download" *ngFor="let attachment of attachments"
                    (click)="download(attachment.attributes.url, attachment.attributes.name)"
                    [title]="attachment.attributes.name">
                    <span class="material-symbols-outlined">{{ getFileIconClass(attachment.attributes.ext) }}</span>
                    <span class="pdf-text text-truncate px-2 text-center">{{ attachment.attributes.name }}</span>
                    <span class="pdf-download">Download <span
                            class="material-symbols-outlined">keyboard_arrow_down</span></span>
                </a>
            </div>

            <div class="date-format d-grid position-relative align-items-center">
                <div class="date-format-box">
                    <div class="date-format-title">Created Date</div>
                    <div class="date d-flex align-items-center"><span
                            class="material-symbols-outlined">calendar_month</span> {{ articleDetails.createdAt | date
                        }}</div>
                </div>
                <div class="date-format-box">
                    <div class="date-format-title">Last Modified Date</div>
                    <div class="date d-flex align-items-center"><span
                            class="material-symbols-outlined">calendar_month</span> {{ articleDetails.updatedAt | date
                        }}</div>
                </div>
                <div class="date-format-box">
                    <div class="date-format-title">Last Modified By</div>
                    <div class="date d-flex align-items-center"><span class="material-symbols-outlined">person</span>
                        {{ (articleDetails.updatedBy?.data?.attributes?.firstname || '') + ' ' +
                        (articleDetails.updatedBy?.data?.attributes?.lastname || '') }}</div>

                </div>
            </div>
        </div>
    </section>
</ng-container>