import { Component, Input, OnInit } from '@angular/core';
import { StoreFrontAPIConstant } from '../../constants/api.constants';
import { OwlOptions } from 'ngx-owl-carousel-o';
import { HomeService } from '../../services/home.service';
import { OperatorFunction, Observable, debounceTime, distinctUntilChanged, tap, switchMap, catchError, of } from 'rxjs';
import { ArticleService } from '../../services/article.service';
import { Router } from '@angular/router';

@Component({
  selector: 'app-banner',
  templateUrl: './banner.component.html',
  styleUrls: ['./banner.component.scss']
})
export class BannerComponent implements OnInit {

  @Input() data: any;

  imgPath: string = StoreFrontAPIConstant.IMG_URL;

  categories: any[] = [];

  selectedCategory: any = null;
  navSpeed = 2000;

  constructor(private service: HomeService, private articleService: ArticleService, private router: Router) { }

  ngOnInit(): void {
    // this.fetchCategories();
    this.navSpeed = this.data.Banner_Timer || this.navSpeed;
    this.customOptions = {
      ...this.customOptions,
      navSpeed: this.navSpeed,
      autoplayTimeout: this.navSpeed,
      autoplaySpeed: this.navSpeed,
    };

  }

  fetchCategories(): void {
    this.service.fetchCategoriesForSearch().subscribe((res) => {
      const categories = res.data;
      const options: any[] = [];
      if (categories && categories.length) {
        for (let i = 0; i < categories.length; i++) {
          const element = categories[i];
          options.push({
            categoryId: element.attributes.slug,
            name: element.attributes.Title
          });
          if (element.attributes.sub_categories?.data?.length) {
            const subCategories = element.attributes.sub_categories?.data;
            for (let i = 0; i < subCategories.length; i++) {
              const subCategory = subCategories[i];
              options.push({
                categoryId: element.attributes.slug,
                subCategoryId: subCategory.attributes.slug,
                name: subCategory.attributes.Title
              });
            }
          }
        }
      }
      this.categories = options.sort((a: any, b: any) => {
        if (a.name < b.name) {
          return -1;
        }
        if (a.name > b.name) {
          return 1;
        }
        return 0;
      });
    })
  }

  selectCategory(data: any) {
    this.selectedCategory = data;
  }

  customOptions: OwlOptions = {
    loop: true,
    autoplay: true,
    dots: false,
    nav: true,
    navSpeed: this.navSpeed,
    autoplayTimeout: this.navSpeed,
    autoplaySpeed: this.navSpeed,
    animateOut: 'fadeOut',
    navText: ["<img src='/assets/images/arrow_back.svg' />", "<img src='/assets/images/arrow_forward.svg' />"],
    responsive: {
      0: {
        items: 1,
      },
      600: {
        items: 1,
      },
      1000: {
        items: 1,
      },
    },
  };

  model: any;
  searching = false;
  searchFailed = false;

  openUrl(data: any) {
    if (data.Banner_URL) {
      window.open(data.Banner_URL, '_blank');
    }
  }

  search: OperatorFunction<string, readonly string[]> = (text$: Observable<string>) =>
    text$.pipe(
      debounceTime(300),
      distinctUntilChanged(),
      tap(() => (this.searching = true)),
      switchMap((term) =>
        this.articleService.searchArticles(term, this.selectedCategory).pipe(
          tap(() => (this.searchFailed = false)),
          catchError(() => {
            this.searchFailed = true;
            return of([]);
          }),
        ),
      ),
      tap(() => (this.searching = false)),
    );


  formatter = (x: { name: string }) => x.name;

  openArticle() {
    const queryParams: any = {
      t: this.model
    };
    this.router.navigate([`/store/article-search`], { queryParams });
  }
}
