import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import moment from 'moment';
import { AuthService, ToastService } from 'ng-snjya';
import { Subject, takeUntil } from 'rxjs';
import { TicketsService } from '../my-activation.service';

@Component({
  selector: 'pmi-my-activation-details',
  templateUrl: './my-activation-details.component.html',
  styleUrls: ['./my-activation-details.component.scss']
})
export class MyActivationDetailsComponent implements OnInit {

  public moment: any = moment;
  public ticketID: any = null;
  public ticket: any = null;
  public sellerDetails: any = {};
  public auth: any = null;
  private ngUnsubscribe = new Subject<void>();
  public loading: any = false;

  constructor(
    private _snackBar: ToastService,
    public dialog: NgbModal,
    public authService: AuthService,
    private activatedRoute: ActivatedRoute,
    private ticketsService: TicketsService
  ) {
    this.auth = this.authService.getAuth();
    this.sellerDetails = {
      ...this.getUserDetails(this.authService.userDetail),
    };
  }

  getUserDetails(userDetail: any) {
    return { email: userDetail.email };
    const obj = {
      customerId: '',
      contactId: ''
    }
    const ud = userDetail?.business_partners[0];
    if (ud) {
      obj.customerId = ud.bp_id;
      if (ud.bp_contacts_map?.length) {
        obj.contactId = ud.bp_contacts_map[0].contact_id;
      }
    }
    return obj;
  }

  ngOnInit() {
    this.activatedRoute.paramMap
      .pipe(takeUntil(this.ngUnsubscribe))
      .subscribe((params) => {
        this.ticketID = params.get("id");
        if (this.ticketID) {
          this.getTicketDetails();
        }
      });
  }

  getTicketDetails() {
    const payload: any = {};
    payload.TICKET = this.ticketID;
    payload.TICKET_TYPE = "SRRQ";
    this.loading = true;
    this.ticketsService
      .getTicketDetails(payload)
      .pipe(takeUntil(this.ngUnsubscribe))
      .subscribe({
        next: (res: any) => {
          this.ticket = res?.data?.SERVICEREQUEST || null;
          this.loading = false;
        },
        error: () => {
          this.loading = false;
          this._snackBar.open("Error while processing your request.", { type: 'Error' });
        },
      });
  }

}
