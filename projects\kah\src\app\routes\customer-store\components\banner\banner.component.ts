import { Component, Input, OnInit } from '@angular/core';
import { StoreFrontAPIConstant } from '../../constants/api.constants';
import { OwlOptions } from 'ngx-owl-carousel-o';

@Component({
  selector: 'app-banner',
  templateUrl: './banner.component.html',
  styleUrls: ['./banner.component.scss']
})
export class BannerComponent implements OnInit {

  @Input() data: any;

  imgPath: string = StoreFrontAPIConstant.IMG_URL;
  navSpeed = 2000;

  ngOnInit(): void {
    this.navSpeed = this.data.Banner_Timer || this.navSpeed;
    this.customOptions = {
      ...this.customOptions,
      navSpeed: this.navSpeed,
      autoplayTimeout: this.navSpeed,
      autoplaySpeed: this.navSpeed,
    };
  }

  customOptions: OwlOptions = {
    loop: true,
    autoplay: true,
    dots: false,
    nav: true,
    navSpeed: this.navSpeed,
    autoplayTimeout: this.navSpeed,
    autoplaySpeed: this.navSpeed,
    animateOut: 'fadeOut',
    navText: ["<img src='/assets/images/arrow_back.svg' />", "<img src='/assets/images/arrow_forward.svg' />"],
    responsive: {
      0: {
        items: 1,
      },
      600: {
        items: 1,
      },
      1000: {
        items: 1,
      },
    },
  };

  openUrl(data: any) {
    if (data.Banner_URL) {
      window.open(data.Banner_URL, '_blank');
    }
  }

}
