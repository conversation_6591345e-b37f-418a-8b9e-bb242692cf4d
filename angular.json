{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"pmi": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}}, "root": "projects/pmi", "sourceRoot": "projects/pmi/src", "prefix": "pmi", "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"outputPath": "dist/pmi", "index": "projects/pmi/src/index.html", "main": "projects/pmi/src/main.ts", "polyfills": ["zone.js", "projects/pmi/src/polyfills.ts"], "tsConfig": "projects/pmi/tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": ["projects/pmi/src/favicon.ico", "projects/pmi/src/assets"], "styles": ["./node_modules/ngx-owl-carousel-o/lib/styles/prebuilt-themes/owl.carousel.min.css", "./node_modules/ngx-owl-carousel-o/lib/styles/prebuilt-themes/owl.theme.default.min.css", "projects/pmi/src/styles.scss"], "scripts": ["./node_modules/jquery/dist/jquery.min.js"], "allowedCommonJsDependencies": ["moment"]}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "500kb", "maximumError": "5mb"}, {"type": "anyComponentStyle", "maximumWarning": "20kb", "maximumError": "20kb"}], "fileReplacements": [{"replace": "projects/pmi/src/environments/environment.ts", "with": "projects/pmi/src/environments/environment.prod.ts"}], "outputHashing": "all", "optimization": {"scripts": true, "styles": {"minify": true, "inlineCritical": false}, "fonts": true}}, "development": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"browserTarget": "pmi:build:production"}, "development": {"browserTarget": "pmi:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"browserTarget": "pmi:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"polyfills": ["zone.js", "zone.js/testing"], "tsConfig": "projects/pmi/tsconfig.spec.json", "inlineStyleLanguage": "scss", "assets": ["projects/pmi/src/favicon.ico", "projects/pmi/src/assets"], "styles": ["projects/pmi/src/styles.scss"], "scripts": []}}}}, "kah": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}}, "root": "projects/kah", "sourceRoot": "projects/kah/src", "prefix": "kah", "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"outputPath": "dist/kah", "index": "projects/kah/src/index.html", "main": "projects/kah/src/main.ts", "polyfills": ["zone.js", "projects/kah/src/polyfills.ts"], "tsConfig": "projects/kah/tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": ["projects/kah/src/favicon.ico", "projects/kah/src/assets"], "styles": ["./node_modules/ngx-owl-carousel-o/lib/styles/prebuilt-themes/owl.carousel.min.css", "./node_modules/ngx-owl-carousel-o/lib/styles/prebuilt-themes/owl.theme.default.min.css", "projects/kah/src/styles.scss"], "scripts": ["./node_modules/jquery/dist/jquery.min.js"], "allowedCommonJsDependencies": ["moment"]}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "500kb", "maximumError": "5mb"}, {"type": "anyComponentStyle", "maximumWarning": "20kb", "maximumError": "20kb"}], "fileReplacements": [{"replace": "projects/kah/src/environments/environment.ts", "with": "projects/kah/src/environments/environment.prod.ts"}], "outputHashing": "all", "optimization": {"scripts": true, "styles": {"minify": true, "inlineCritical": false}, "fonts": true}}, "development": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"browserTarget": "kah:build:production"}, "development": {"browserTarget": "kah:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"browserTarget": "kah:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"polyfills": ["zone.js", "zone.js/testing"], "tsConfig": "projects/kah/tsconfig.spec.json", "inlineStyleLanguage": "scss", "assets": ["projects/kah/src/favicon.ico", "projects/kah/src/assets"], "styles": ["projects/kah/src/styles.scss"], "scripts": []}}}}}, "cli": {"analytics": "b7ae8ec8-ce03-4e3c-90b6-53457c031c5d"}}