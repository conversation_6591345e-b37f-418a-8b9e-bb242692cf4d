<ng-container *ngIf="data?.extra_links_list.length">
    <section class="other-links-sec">
        <div class="other-l-body d-flex"
            style="background: url('{{data?.extra_links_bg_img.data.attributes.url}}') center center no-repeat;">
            <div class="other-l-box"></div>
            <div class="other-l-box">
                <h2>{{data?.extra_links_title}}</h2>
                <ul class="d-flex flex-column">
                    <ng-container *ngFor="let extraLinkList of data?.extra_links_list">
                        <li *ngIf="extraLinkList?.extra_links_list_url">
                            <a [href]="[extraLinkList?.extra_links_list_url]" class="d-flex flex-column">
                                <span
                                    class="material-symbols-outlined icon-check">{{extraLinkList?.extra_links_list_icon}}</span>
                                <span class="other-l-title">{{extraLinkList?.extra_links_list_text}}</span>
                                <span class="other-l-date">{{extraLinkList?.extra_links_list_date}}</span>
                                <span class="material-symbols-outlined icon-east">east</span>
                            </a>
                        </li>
                    </ng-container>
                </ul>
            </div>
        </div>
    </section>
</ng-container>