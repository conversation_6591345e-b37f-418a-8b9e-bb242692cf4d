import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { BannerComponent } from './banner/banner.component';
import { ExtralinksComponent } from './extralinks/extralinks.component';
import { HomecategoryAndSdebarComponent } from './homecategory-and-sdebar/homecategory-and-sdebar.component';
import { PizzaworkComponent } from './pizzawork/pizzawork.component';
import { StoremanagerComponent } from './storemanager/storemanager.component';
import { FormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { DynamicComponentDirective } from '../directives/dynamic-component.directive';
import { CarouselModule } from 'ngx-owl-carousel-o';
import { UpcomingNewsComponent } from './upcoming-news/upcoming-news.component';
import { WebinarsComponent } from './webinars/webinars.component';
import { NgbDropdownModule, NgbTypeaheadModule } from '@ng-bootstrap/ng-bootstrap';
import { NewsFeedsComponent } from './news-feeds/news-feeds.component';
import { AskPermissionComponent } from './ask-permission/ask-permission.component';

const components = [
  BannerComponent,
  UpcomingNewsComponent,
  HomecategoryAndSdebarComponent,
  ExtralinksComponent,
  PizzaworkComponent,
  StoremanagerComponent,
  DynamicComponentDirective,
  UpcomingNewsComponent,
  WebinarsComponent,
  AskPermissionComponent,
  NewsFeedsComponent,
]

@NgModule({
  declarations: [
    ...components,
  ],
  imports: [
    CommonModule,
    FormsModule,
    RouterModule,
    CarouselModule,
    NgbDropdownModule,
    NgbTypeaheadModule
  ],
  exports: [
    FormsModule,
    ...components
  ]
})
export class StoreSharedModule { }
