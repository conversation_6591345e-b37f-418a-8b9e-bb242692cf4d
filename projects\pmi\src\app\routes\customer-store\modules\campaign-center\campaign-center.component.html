<section #scrollContainer class="banner-sec position-relative"
    style="background: url('/assets/images/campaign-center.jpg') center center no-repeat;">
    <div class="banner-body position-relative d-flex flex-column">
        <h1>Campaign Center</h1>
        <div class="tab-buttons d-flex align-items-center">
            <button type="button" class="tab-btn selected">Request Form</button>
            <button type="button" class="tab-btn" [routerLink]="['..', 'my-activation']">My text
                Activations</button>
        </div>
    </div>
</section>
<section class="bedcrumbs-sec">
    <div class="bedcrumbs m-0 d-flex align-items-center"> <span class="home-link cursor-pointer"
            routerLink="/store/home">Home</span> <span class="material-symbols-outlined">keyboard_arrow_right</span>
        <span class="home-link">Campaign center</span>
    </div>
</section>
<section class="campaign-sec">
    <div class="d-flex w-100 h-100 justify-content-center align-items-center" *ngIf="loading">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Loading...</span>
        </div>
    </div>
    <ng-container *ngIf="!loading">
        <!--STEP 1-->
        <div class="step" [class.active]="selectedStep == 1">
            <div class="campaign-body d-flex position-relative">
                <div class="campaign-left">
                    <h3>{{ pageDetails.Step_1_Title }}</h3>
                    <div [innerHtml]="step1Desc"></div>
                    <ul>
                        <li>Account Id: {{ getUserDetails().customerId }}</li>
                        <li>Requester: {{ userDetails.display_name }}</li>
                        <li>Email: {{ userDetails.email }}</li>
                        <li>Contact Id: {{ getUserDetails().contactId }}</li>
                        <!-- <li>Phone: +1 **********</li> -->
                    </ul>

                    <div class="campaign-left-form">
                        <form class="d-flex form">
                            <div class="form-group">
                                <label>Send Date</label>
                                <input type="date" class="form-control" [(ngModel)]="dataToSend.sendDate"
                                    [ngModelOptions]="{standalone: true}" [min]="minDate" />
                            </div>
                            <div class="form-group">
                                <label>Send Time (Specific Time)</label>
                                <select class="form-control" [(ngModel)]="dataToSend.sendTime"
                                    [ngModelOptions]="{standalone: true}">
                                    <option *ngFor="let t of sendTimes" [value]="t.description">{{ t.description }}
                                    </option>
                                </select>
                            </div>
                        </form>
                        <div class="form-check mt-3">
                            <input class="form-check-input" type="checkbox" [value]="true"
                                [(ngModel)]="dataToSend.useLocalTZ" id="flexCheckDefault">
                            <label class="form-check-label" for="flexCheckDefault">
                                Use Local Market Timezone instead of Pacific Time?
                            </label>
                        </div>
                    </div>
                </div>
                <div class="campaign-right">
                    <h3>DMA Markets</h3>
                    <form class="d-flex flex-column position-relative form">
                        <label class="form-lavel" *ngFor="let dm of dms">
                            <div class="check-box"> <span class="material-symbols-outlined">check</span> </div>
                            <input type="checkbox" class="form-control"
                                [checked]="dataToSend.markets.includes(dm.authorized_market?.description || '')"
                                (change)="onMarket($event, dm)" />
                            {{ dm.authorized_market?.description }}
                        </label>
                    </form>
                </div>
            </div>
        </div>

        <!--STEP 2-->
        <div class="step" [class.active]="selectedStep == 2">
            <div class="campaign-body d-flex position-relative">
                <div class="campaign-right full-width">
                    <h3>Select Your Message Type</h3>
                    <div class="campaign-left-form">
                        <form class="d-flex form">
                            <div class="form-group">
                                <label>Message Type</label>
                                <select class="form-control" [(ngModel)]="dataToSend.messageType"
                                    [ngModelOptions]="{standalone: true}" (ngModelChange)="dataToSend.offer = ''">
                                    <option *ngFor="let t of messageTypes" [value]="t.text">{{ t.text }}</option>
                                </select>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <div class="campaign-body d-flex position-relative">
                <div class="campaign-left">
                    <h3>{{ pageDetails.Step_2_Title }}</h3>
                    <p>{{ pageDetails.Step_2_Description_above }}</p>
                    <div class="campaign-left-form">
                        <form class="d-flex form">
                            <div class="form-group">
                                <label>*Offer End Date</label>
                                <input type="date" class="form-control" [(ngModel)]="dataToSend.offerEndDate"
                                    [min]="minDate" [ngModelOptions]="{standalone: true}" />
                            </div>
                        </form>
                        <form class="d-flex form mt-3">
                            <div class="form-group" *ngIf="showCustomField">
                                <label>*Custom Message</label>
                                <input type="text" class="form-control" [(ngModel)]="dataToSend.customMessage"
                                    [ngModelOptions]="{standalone: true}" />
                            </div>
                        </form>
                    </div>
                    <br>
                    <p>{{ pageDetails.Step_2_Description_below }}</p>
                    <div [innerHTML]="step2MessageLayout"></div>
                </div>
                <div class="campaign-right">
                    <h3>Select your Message</h3>
                    <form class="d-flex flex-column position-relative form">
                        <label class="form-lavel radio-form-label" *ngFor="let offer of getMessages()">
                            <div class="check-box"> <span class="material-symbols-outlined">check</span> </div>
                            <input type="radio" class="form-control" [value]="offer.message_text?.description || ''"
                                [(ngModel)]="dataToSend.offer" [ngModelOptions]="{standalone: true}"
                                (ngModelChange)="messageChanged(offer)" />
                            {{ offer.message_text?.description }}
                        </label>
                    </form>
                </div>
            </div>
        </div>
        <!--STEP 3-->
        <div class="step" [class.active]="selectedStep == 3">
            <div class="campaign-body d-flex position-relative">
                <div class="campaign-left full-width">
                    <h3>{{ pageDetails.Step_3_Title }}</h3>
                    <div [innerHTML]="step3Desc"></div>
                    <div class="campaign-box-list position-relative d-grid">
                        <div class="campaign-box">
                            <h3><span class="material-symbols-outlined">event_upcoming</span> Send Date: </h3>
                            <p>{{ dataToSend.sendDate | date }}</p>
                        </div>
                        <div class="campaign-box">
                            <h3><span class="material-symbols-outlined">schedule</span> Send Time (Pacific Time):</h3>
                            <p>{{ dataToSend.sendTime }}</p>
                        </div>
                        <!-- <div class="campaign-box">
                            <h3><span class="material-symbols-outlined">where_to_vote</span> Use local time zone?</h3>
                            <p>No</p>
                        </div> -->
                        <div class="campaign-box">
                            <h3><span class="material-symbols-outlined">event</span>Offer End Date:</h3>
                            <p>{{ dataToSend.offerEndDate | date }}</p>
                        </div>
                        <div class="campaign-box">
                            <h3><span class="material-symbols-outlined">mark_email_read</span> Selected Message Type:
                            </h3>
                            <p>{{ dataToSend.messageType }}</p>
                            <!-- <p>{{ messageType(dataToSend.messageType) }}</p> -->
                        </div>
                        <div class="campaign-box">
                            <h3><span class="material-symbols-outlined">chat</span> Selected text message:</h3>
                            <!-- <p>{{ getoffer(dataToSend.offer) }}</p> -->
                            <p>{{ dataToSend.offer }}</p>
                        </div>
                        <div class="campaign-box">
                            <h3><span class="material-symbols-outlined">store</span> Target Markets:</h3>
                            <!-- <p>{{ getMarkets(dataToSend.markets) }}</p> -->
                            <p>{{ dataToSend.markets.join(', ') }}</p>
                        </div>
                        <div class="campaign-box">
                            <h4>By clicking Submit, you are submitting your request.</h4>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!--STEP 4-->
        <div class="step" [class.active]="selectedStep == 4">
            <div class="campaign-body d-flex position-relative">
                <div class="campaign-left full-width">
                    <h3>{{ pageDetails.Success_Title }}</h3>
                    <p>{{ pageDetails.Success_Description }}</p>

                    <div class="campaign-box-list position-relative d-grid">
                        <div class="campaign-box">
                            <h3><span class="material-symbols-outlined">event_upcoming</span> Send Date: </h3>
                            <p>{{ dataToSend.sendDate | date }}</p>
                        </div>
                        <div class="campaign-box">
                            <h3><span class="material-symbols-outlined">schedule</span> Send Time (Pacific Time):</h3>
                            <p>{{ dataToSend.sendTime }}</p>
                        </div>
                        <!-- <div class="campaign-box">
                            <h3><span class="material-symbols-outlined">where_to_vote</span> Use local time zone?</h3>
                            <p>No</p>
                        </div> -->
                        <div class="campaign-box">
                            <h3><span class="material-symbols-outlined">event</span>Offer End Date:</h3>
                            <p>{{ dataToSend.offerEndDate | date }}</p>
                        </div>
                        <div class="campaign-box">
                            <h3><span class="material-symbols-outlined">mark_email_read</span> Selected Message Type:
                            </h3>
                            <p>{{ dataToSend.messageType }}</p>
                            <!-- <p>{{ messageType(dataToSend.messageType) }}</p> -->
                        </div>
                        <div class="campaign-box">
                            <h3><span class="material-symbols-outlined">chat</span> Selected text message:</h3>
                            <!-- <p>{{ getoffer(dataToSend.offer) }}</p> -->
                            <p>{{ dataToSend.offer }}</p>
                        </div>
                        <div class="campaign-box">
                            <h3><span class="material-symbols-outlined">store</span> Target Markets:</h3>
                            <!-- <p>{{ getMarkets(dataToSend.markets) }}</p> -->
                            <p>{{ dataToSend.markets.join(', ') }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="campaign-submit-body d-flex align-items-center">
            <button type="button" class="submit-btn prev" id="prev" (click)="prev()" *ngIf="selectedStep != 4"
                [disabled]="selectedStep == 1"><span class="material-symbols-outlined">arrow_left_alt</span>
                Previous</button>
            <button type="button" class="submit-btn" id="next" (click)="next()" *ngIf="selectedStep != 4"
                [disabled]="selectedStep == 4 || isNextDisabled()">{{ selectedStep == 3 ? (saving ? 'Submitting' :
                'Submit') : 'Next'}}
                <span class="material-symbols-outlined">arrow_right_alt</span></button>
            <button type="button" class="submit-btn" id="next" (click)="createNew()" *ngIf="selectedStep == 4">Create
                New</button>
        </div>
    </ng-container>
</section>