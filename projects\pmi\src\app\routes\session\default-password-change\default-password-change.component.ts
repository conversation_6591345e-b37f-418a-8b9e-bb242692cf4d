import { Component, Inject } from '@angular/core';
import {
  AbstractControl,
  FormBuilder,
  FormGroup,
  ValidationErrors,
  ValidatorFn,
  Validators,
} from '@angular/forms';
import { Router } from '@angular/router';
import { AuthService, ToastService, UsersService } from 'ng-snjya';
import { HomeService } from '../../customer-store/services/home.service';
import { ComponentNameConstants } from '../../customer-store/constants/components.contants';
import { ROUTER_URL } from '../../ng-snjya.config.token';

function ConfirmedValidator(controlName: string, matchingControlName: string) {
  return (formGroup: FormGroup) => {
    const control = formGroup.controls[controlName];
    const matchingControl = formGroup.controls[matchingControlName];
    if (
      matchingControl.errors &&
      !matchingControl.errors['confirmedValidator']
    ) {
      return;
    }
    if (control.value !== matchingControl.value) {
      matchingControl.setErrors({ confirmedValidator: true });
    } else {
      matchingControl.setErrors(null);
    }
  };
}

function patternValidator(regex: RegExp, error: ValidationErrors): ValidatorFn {
  return (control: AbstractControl) => {
    if (!control.value) {
      // if control is empty return no error
      return null;
    }

    // test the value of the control against the regexp supplied
    const valid = regex.test(control.value);

    // if true, return no error (no error), else return error passed in the second parameter
    return valid ? null : error;
  };
}

@Component({
  selector: 'pmi-default-password-change',
  templateUrl: './default-password-change.component.html',
  styleUrls: ['./default-password-change.component.scss'],
})
export class DefaultPasswordChangeComponent {
  form: FormGroup = this.formBuilder.group(
    {
      password: [
        '',
        [
          Validators.required,
          Validators.minLength(8),
          // check whether the entered password has a number
          patternValidator(/\d/, {
            hasNumber: true,
          }),
          // check whether the entered password has upper case letter
          patternValidator(/[A-Z]/, {
            hasCapitalCase: true,
          }),
          // check whether the entered password has a lower case letter
          patternValidator(/[a-z]/, {
            hasSmallCase: true,
          }),
          // check whether the entered password has a special character
          patternValidator(/[ !@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/, {
            hasSpecialCharacters: true,
          }),
        ],
      ],
      passwordConfirm: ['', Validators.required],
    },
    {
      validators: ConfirmedValidator('password', 'passwordConfirm'),
    }
  );
  public submitted = false;
  public saving = false;
  loading = false;
  footerData: any = {};

  constructor(
    private formBuilder: FormBuilder,
    private authService: AuthService,
    public router: Router,
    private _snackBar: ToastService,
    private userService: UsersService,
    public homeService: HomeService,
    @Inject(ROUTER_URL) private router_url: any
  ) {}

  ngOnInit(): void {}

  get f(): { [key: string]: AbstractControl } {
    return this.form.controls;
  }

  fetchData() {
    this.loading = true;
    this.homeService.getHomePageDetails().subscribe({
      next: (res: any) => {
        this.footerData = this.homeService.getDataByComponentName(
          res?.data?.attributes?.Body || [],
          ComponentNameConstants.Footer
        );
        console.log(res);
        this.loading = false;
      },
    });
  }

  onSubmit(): void {
    this.submitted = true;
    if (this.form.invalid) {
      return;
    }
    const userDetail = this.authService.userDetail;
    if (!userDetail.token || !userDetail.id) {
      this._snackBar.open('Invalid Url.', { type: 'Error' });
      return;
    }
    this.saving = true;
    this.userService
      .updateUserPassword(userDetail.id, {
        is_password_change: true,
        password: this.authService.encryptString(this.form.value.password),
      })
      .subscribe({
        complete: () => {
          this.router.navigate([`/${this.router_url.STOREFRONT}/dashboard`]);
          this._snackBar.open('Password Changed successfully!');
        },
        error: (err: any) => {
          this._snackBar.open('Error while processing your request.', {
            type: 'Error',
          });
        },
      });
  }
}
