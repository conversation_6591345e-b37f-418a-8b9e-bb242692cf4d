import { Component, Input } from '@angular/core';
import { StoreFrontAPIConstant } from '../../constants/api.constants';

@Component({
  selector: 'app-footer',
  templateUrl: './footer.component.html',
  styleUrls: ['./footer.component.scss']
})
export class FooterComponent {

  @Input() footerLinksdata: any = {};
  @Input() footerData: any = {};

  imgPath: string = StoreFrontAPIConstant.IMG_URL;

  backToTop() {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  }

}
