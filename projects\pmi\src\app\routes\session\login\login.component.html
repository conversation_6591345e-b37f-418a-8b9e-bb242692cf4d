<div class="d-flex flex-md-row p-0 sign_section gap-3">

    <div class="col-sm-12 d-flex flex-row w-100">
        <div class="col-md-12 col-lg-6 d-lg-flex d-none home-banner-sec h-100">
            <div class="home-banner-img advt-banner-section">
                <owl-carousel-o [options]=" loginOptions">
                    <ng-container *ngFor="let slideImage of loginData?.Login_Page_Slide_Image?.data">
                        <ng-template class="slide" carouselSlide [id]="slideImage.id.toString()">
                            <div class="custom-slide">
                                <img [src]="slideImage.attributes.url" alt="">
                            </div>
                        </ng-template>
                    </ng-container>
                </owl-carousel-o>
            </div>
        </div>
        <div class="col col-md-12 col-lg-6 h-100 px-lg-4 px-0">
            <div class="flex-grow-1 d-flex flex-column justify-content-between h-100">

                <div class="col-sm-12 px-lg-0 px-3">
                    <a class="navbar-brand mt-3" href="#"><img src="assets/images/logo/logo-red.png"
                            class="img-fluid"></a>
                </div>

                <div class="login_box p-4 col-sm-12">
                    <h1 class="mb-0 fs-2rem">{{loginData?.Welcome_Login_Title}}</h1>
                    <span *ngIf="showCorporateForm || showFranchiseesForm">{{loginData?.Welcome_Login_Text_2}}</span>
                    <span *ngIf="!showCorporateForm && !showFranchiseesForm">{{loginData?.Welcome_Login_Text_1}}</span>

                    <!-- franchisees-section -->
                    <div class="col-sm-12 custom-box franchisees-section mt-4" *ngIf="!showCorporateForm">
                        <div class="login-header d-flex flex-row justify-content-between align-items-center"
                            (click)="toggleFranchiseesForm()">
                            <div class="d-flex flex-column">
                                <h4 class="fs-5">{{ loginData?.Login_Box_Details?.length ? loginData?.Login_Box_Details[0].Login_Box_Title : '' }}</h4>
                                <p class="fs-6 mt-3">{{ loginData?.Login_Box_Details?.length ? loginData?.Login_Box_Details[0].Login_Box_Subtitle : '' }}</p>
                            </div>
                            <div [ngClass]="{'up-down-arrow': true, 'rotate-arrow': showFranchiseesForm}"><img
                                    src="assets/images/keyboard_arrow.svg" class="img-fluid">
                            </div>
                        </div>
                        <form class="form-field-full mt-4" [formGroup]="loginForm" *ngIf="showFranchiseesForm">
                            <div class="form-group user-name">
                                <label for="exampleInputEmail1">Email Address</label>
                                <input type="email" class="form-control" id="username" formControlName="email"
                                    placeholder="Enter email" autocomplete="" />
                            </div>
                            <div class="form-group user-pass mt-4 mb-0">
                                <label for="exampleInputPassword1">Password</label>
                                <div class="position-relative">
                                    <input [type]="showPassword ? 'text': 'password'" class="form-control"
                                        formControlName="password" placeholder="Enter Password" />
                                    <i class="material-symbols-outlined visible-pass" *ngIf="!showPassword"
                                        (click)="showPassword = !showPassword">visibility</i>
                                    <i class="material-symbols-outlined visible-pass" *ngIf="showPassword"
                                        (click)="showPassword = !showPassword">
                                        visibility_off
                                    </i>
                                </div>
                                <span class="form-text hint" role="button" [routerLink]="'../forgot-password'">I
                                    forgot my password</span>
                            </div>
                            <div class="button-section mt-4">
                                <button type="submit" class="btn btn-light w-100 btn-login"
                                    [disabled]="!!loginForm.invalid || isSubmitting" (click)="login()">Log in</button>
                            </div>
                            <p class="text-danger" *ngIf="errMsg">{{errMsg}}</p>
                        </form>
                    </div>

                    <!-- corporate-section -->
                    <div class="col-sm-12 mt-4 custom-box corporate-section" *ngIf="!showFranchiseesForm">
                        <div class="login-header d-flex flex-row justify-content-between align-items-center"
                            (click)="toggleCorporateForm()">
                            <div class="d-flex flex-column">
                                <h4 class="fs-5">{{ loginData?.Login_Box_Details?.length ? loginData?.Login_Box_Details[1].Login_Box_Title : '' }}</h4>
                                <p class="fs-6 mt-3">{{ loginData?.Login_Box_Details?.length ? loginData?.Login_Box_Details[1].Login_Box_Subtitle : '' }}</p>
                            </div>
                            <div [ngClass]="{'up-down-arrow': true, 'rotate-arrow': showCorporateForm}"><img
                                    src="assets/images/keyboard_arrow.svg" class="img-fluid">
                            </div>
                        </div>
                        <form class="form-field-full mt-4" [formGroup]="loginForm" *ngIf="showCorporateForm">
                            <div class="form-group user-sso">
                                <select class="form-control" formControlName="selectedTenant">
                                    <option *ngFor="let tenant of tenantArr" [value]="tenant.value">
                                        {{ tenant.name }}
                                    </option>
                                </select>
                                <a [attr.href]="API_ENDPOINT + '/auth/signin?tenant='+selectedTenant.value+'&rorigin='+rorigin" class="sso-btn">Login with SSO</a>
                            </div>
                            <div class="form-group user-name">
                                <label for="exampleInputEmail1">USER NAME</label>
                                <input type="text" class="form-control" id="username" formControlName="email"
                                    placeholder="Enter user name" autocomplete="" />
                            </div>
                            <div class="form-group user-pass mt-4 mb-0">
                                <label for="exampleInputPassword1">Password</label>
                                <div class="position-relative">
                                    <input [type]="showPassword ? 'text': 'password'" class="form-control"
                                        formControlName="password" placeholder="Enter Password" />
                                    <i class="material-symbols-outlined visible-pass" *ngIf="!showPassword"
                                        (click)="showPassword = !showPassword">visibility</i>
                                    <i class="material-symbols-outlined visible-pass" *ngIf="showPassword"
                                        (click)="showPassword = !showPassword">
                                        visibility_off
                                    </i>
                                </div>
                                <span class="form-text hint decoration-none" role="button"
                                    [routerLink]="'../forgot-password'">I forgot my password</span>
                            </div>
                            <div class="button-section mt-4">
                                <button type="submit" class="btn btn-light w-100 btn-login"
                                    [disabled]="!!loginForm.invalid || isSubmitting" (click)="login()">Log in</button>
                            </div>
                            <p class="text-danger" *ngIf="errMsg">{{errMsg}}</p>
                        </form>
                    </div>

                </div>
                <div
                    class="col-sm-12 d-flex flex-column flex-xl-row border-top align-items-center justify-content-between py-3">
                    <div class="copy-right fs-7">
                        {{footerData?.footer_copyright}}
                    </div>
                    <ul class="nav-link mb-0 d-flex flex-row gap-3 fs-7">
                        <ng-container *ngFor="let bottomFooterLinks of footerData?.Bottom_Footer_Links">
                            <li *ngIf="bottomFooterLinks?.footer_link">
                                <a [href]="[bottomFooterLinks?.footer_link]" target="_blank">{{bottomFooterLinks?.footer_link_name}}</a>
                            </li>
                        </ng-container>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>