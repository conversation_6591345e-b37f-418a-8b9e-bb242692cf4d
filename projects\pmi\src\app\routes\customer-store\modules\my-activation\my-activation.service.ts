import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { StoreFrontAPIConstant } from '../../constants/api.constants';
import { map, of } from 'rxjs';
import { AuthService } from 'ng-snjya';

@Injectable({
  providedIn: 'root'
})
export class TicketsService {
  constructor(
    private http: HttpClient,
    private authService: AuthService
  ) { }

  getAll(data: any) {
    return this.http.post<any>(StoreFrontAPIConstant.ListMarketingTicket, data);
  }

  getTicketDetails(data: any) {
    return this.http.post(StoreFrontAPIConstant.DetailMarketingTicket, data);
  }

  getAllTicketStatus() {
    return this.http.get<any>(StoreFrontAPIConstant.TICKET_STATUSES);
  }

  hasPermission() {
    let params = new HttpParams();
    params = params.set('userId', this.authService.userDetail.id)
    return this.http.get<any>(
      `${StoreFrontAPIConstant.DMs}`, { params }
    ).pipe(map(data => {
      return !!data.data.length;
    }));
  }

}
