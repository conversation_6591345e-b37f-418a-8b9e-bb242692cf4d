<section class="banner-sec position-relative"
    style="background: url('/assets/images/campaign-center.jpg') center center no-repeat;">
    <div class="banner-body position-relative d-flex flex-column">
        <h1>My Text Activations</h1>
        <div class="tab-buttons d-flex align-items-center">
            <button type="button" class="tab-btn" [routerLink]="['..', 'campaign-center']">Request
                Form</button>
            <button type="button" class="tab-btn selected">My text Activations</button>
        </div>
    </div>
</section>
<section class="bedcrumbs-sec">
    <div class="bedcrumbs m-0 d-flex align-items-center"> <span class="home-link cursor-pointer" routerLink="/store/home">Home</span> <span
            class="material-symbols-outlined">keyboard_arrow_right</span> <span class="home-link cursor-pointer" routerLink="/store/campaign-center">Campaign center</span>
    </div>
</section>


<section class="ticket-status-sec">
    <div class="ticket-status-body">
        <h3>My Text Activations</h3>
        <form class="ticket-status-form all-form-res" [formGroup]="filterForm">
            <div class="form">
                <div class="form-group">
                    <label><span class="material-icons-outlined">calendar_month</span> Date From</label>
                    <div class="input-group">
                        <input class="form-control" name="picker1" formControlName="DATE_FROM" ngbDatepicker
                            #d="ngbDatepicker" [maxDate]="today()" />
                        <button class="btn btn-outline-secondary" (click)="d.toggle()" type="button">
                            <i class="material-icons-outlined">
                                calendar_month
                            </i>
                        </button>
                    </div>
                </div>
                <div class="form-group">
                    <label><span class="material-icons-outlined">calendar_month</span> Date To</label>
                    <div class="input-group">
                        <input class="form-control" name="picker2" formControlName="DATE_TO" ngbDatepicker
                            #d1="ngbDatepicker" [minDate]="f.DATE_FROM.value" [maxDate]="today()" />
                        <button class="btn btn-outline-secondary" (click)="d1.toggle()" type="button">
                            <i class="material-icons-outlined">
                                calendar_month
                            </i>
                        </button>
                    </div>
                </div>
                <div class="form-group">
                    <label><span class="material-icons-outlined">feed</span> Status</label>
                    <select class="form-control select-arrow-down" formControlName="STATUS">
                        <option *ngFor="let status of statuses" [value]="status.code">{{status.description}}</option>
                    </select>
                </div>
                <div class="form-group o-num">
                    <label><span class="material-icons-outlined">pin</span> Service Ticket #</label>
                    <input type="input" class="form-control" placeholder="Service Ticket #"
                        (keyup.enter)="getTicketHistory()" formControlName="TICKET">
                </div>
                <p class="text-danger search-error" *ngIf="submitted && filterForm.touched && filterForm.invalid">
                    Please add a valid from and to date
                </p>
                <div class="form-btn-sec d-flex gap-1 justify-content-center">
                    <button type="button" class="mx-4 tab-btn selected" (click)="clear()">Clear</button>
                    <button type="button" class="mx-4 tab-btn selected" (click)="search()" [disabled]="loading">{{loading ?
                        'Searching...' : 'Search'}}</button>
                </div>
            </div>
        </form>
        <div class="ticket-s-table">
            <snjya-grid [columns]="columnDefs" (rowClick)="goToTicket($event)" [data]="tickets" [showExport]="false"
                *ngIf="!loading && tickets.length"></snjya-grid>
            <div class="w-100" *ngIf="loading || !tickets.length">{{ loading ? 'Loading...' : 'No records found.'
                }}
            </div>
        </div>
    </div>
</section>