import { Component, Inject, OnInit } from '@angular/core';
import {
  Router,
  NavigationStart,
  NavigationEnd,
  NavigationCancel,
  NavigationError,
} from '@angular/router';

import { LoadingService } from './shared/components/loading-bar/loading.service';
import { AuthService, ToastService } from 'ng-snjya';
import { ROUTER_URL } from './routes/ng-snjya.config.token';

@Component({
  selector: 'kah-root',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.scss'],
})
export class AppComponent implements OnInit {
  constructor(
    private loadingService: LoadingService,
    public router: Router,
    private _snackBar: ToastService,
    private auth: AuthService,
    @Inject(ROUTER_URL) private router_url: any
  ) {
    // Display error message while user try to login with "Microsoft SSO" button.
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.has('msalerror')) {
      const msalerror: any = urlParams.get('msalerror');
      this._snackBar.open(msalerror, { type: 'Error' });
    }
    if (urlParams.has('authtoken')) {
      const user = this.auth.getAuth();
      if (['EMPLOYEE_CMS', 'EMPLOYEE_ADMIN', 'EMPLOYEE'].includes(user?.code)) {
        if (user && user?.strapiUser && user?.strapiToken) {
          localStorage.setItem('userInfo', JSON.stringify(user?.strapiUser));
          if (['EMPLOYEE_CMS', 'EMPLOYEE_ADMIN'].includes(user?.code)) {
            localStorage.setItem('jwtToken', JSON.stringify(user?.strapiToken));
          }
        }
        if (urlParams.has('redirect')) {
          window.location.href =
            '/admin/content-manager/collection-types/api::article.article?page=1&pageSize=10&sort=Heading:ASC';
        } else {
          const redirectUrl = localStorage.getItem('redirectUrl');
          if (redirectUrl) {
            localStorage.removeItem('redirectUrl');
            this.router.navigateByUrl(redirectUrl);
          } else {
            this.router.navigate([`/${this.router_url.STOREFRONT}`]);
          }
        }
      }
    }
  }

  ngOnInit(): void {
    this.router.events.subscribe((ev) => {
      if (ev instanceof NavigationStart) {
        this.loadingService.show();
      }
      if (
        ev instanceof NavigationEnd ||
        ev instanceof NavigationCancel ||
        ev instanceof NavigationError
      ) {
        this.loadingService.hide();
      }
    });
  }
}
