import { ComponentRef, Directive, Input, OnInit, ViewContainerRef } from '@angular/core';
import { BannerComponent } from '../components/banner/banner.component';
import { ExtralinksComponent } from '../components/extralinks/extralinks.component';
import { HomecategoryAndSdebarComponent } from '../components/homecategory-and-sdebar/homecategory-and-sdebar.component';
import { PizzaworkComponent } from '../components/pizzawork/pizzawork.component';
import { StoremanagerComponent } from '../components/storemanager/storemanager.component';
import { ComponentNameConstants } from '../constants/components.contants';
import { UpcomingNewsComponent } from '../components/upcoming-news/upcoming-news.component';
import { WebinarsComponent } from '../components/webinars/webinars.component';
import { NewsFeedsComponent } from '../components/news-feeds/news-feeds.component';

@Directive({
  selector: '[appDynamicComponent]'
})
export class DynamicComponentDirective implements OnInit {

  @Input() componentName: any;
  @Input() data: any;
  @Input() allData: any;

  componenets: any = {
    [ComponentNameConstants.Banner]: BannerComponent,
    [ComponentNameConstants.Upcomingnews]: UpcomingNewsComponent,
    [ComponentNameConstants.Category]: HomecategoryAndSdebarComponent,
    [ComponentNameConstants.ExtraLinks]: ExtralinksComponent,
    [ComponentNameConstants.PizzaWork]: PizzaworkComponent,
    [ComponentNameConstants.StoreManager]: StoremanagerComponent,
    [ComponentNameConstants.Webinar]: WebinarsComponent,
    [ComponentNameConstants.newsfeeds]: NewsFeedsComponent
  }

  constructor(
    private viewContainerRef: ViewContainerRef
  ) { }

  ngOnInit() {
    this.loadComponent();
  }

  private loadComponent() {
    const component = this.getComponentByName(this.componentName);
    if (component) {
      const c = this.viewContainerRef.createComponent(component) as ComponentRef<any>;
      c.instance.data = this.data;
      c.instance.allData = this.allData;
    } else {
      console.error(`Component ${this.componentName} not found!`);
    }
  }

  private getComponentByName(name: string) {
    return this.componenets[name];
  }
}
