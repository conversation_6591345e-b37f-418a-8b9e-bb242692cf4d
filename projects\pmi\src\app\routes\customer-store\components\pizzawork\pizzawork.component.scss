/*----SUBMIT A PIZZA SEC----*/
.submit-a-pizza-sec {
    margin: 0;
    padding: 60px 0;
    position: relative;
    background: url(/assets/images/background-img.jpg) center -201px no-repeat;
    background-size: cover;
    height: auto;

    .submit-a-pizza-body {
        margin: 0 auto;
        padding: 0 30px;
        max-width: 1500px;
        width: 100%;
        position: relative;
        gap: 80px 0;

        .submit-a-pizza-work {
            position: relative;
            text-align: center;
            display: flex;
            flex-direction: column;
            gap: 30px 0;

            .submit-a-pizza-logo {
                max-width: 220px;
                margin: 0 auto;
                position: relative;

                img {
                    width: 100%;
                }
            }

            .submit-a-pizza-title {
                margin: 0;
                padding: 0;
                position: relative;
                line-height: 32px;
                font-size: var(--brand-font-size-5);
                color: var(--brand-color-tertiary-dark-teal);
                font-weight: var(--brand-font-weight-medium);
            }

            .submit-a-pizza-desc {
                margin: 0;
                padding: 0;
                position: relative;
                line-height: 24px;
                font-family: var(--brand-font-family-secondary);
                font-size: 22px;
                color: var(--brand-color-black);
                font-weight: var(--brand-font-weight-light);
            }

            .submit-a-pizza-btn {
                margin: 30px 0 0 0;
                gap: 0 30px;

                a {
                    margin: 0;
                    padding: 0;
                    position: relative;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    width: 380px;
                    height: 50px;
                    font-size: var(--brand-font-size-0-9);
                    font-weight: var(--brand-font-weight-semi-bold);
                    color: var(--brand-color-primary);
                    gap: 0 5px;
                    text-transform: uppercase;
                    background: var(--brand-color-white);
                    border-radius: 8px;
                    letter-spacing: 2px;
                }
            }
        }


        .signage-and-list {
            position: relative;
            margin: 0;
            gap: 0 5%;

            .signage-sec,
            .list-sec {
                margin: 0;
                padding: 30px;
                position: relative;
                flex: 1;
                height: 450px;
                background: var(--brand-color-white);
                border-radius: 20px;
            }

            .signage-sec {
                background: url(/assets/images/signage-box-bg.jpg) center center no-repeat;
                background-size: cover;

                .signage-left {
                    position: relative;
                    flex: 1;

                    .signage-title {
                        margin: 40px 0 20px 0;
                        padding: 0;
                        position: relative;
                        line-height: 42px;
                        font-size: 36px;
                        color: var(--brand-color-secondary-neutral-charcoal);
                        font-weight: var(--brand-font-weight-bold);
                    }

                    .signage-p {
                        margin: 0 0 50px 0;
                        padding: 0;
                        position: relative;
                        font-family: var(--brand-font-family-secondary);
                        font-size: var(--brand-font-size-1-125);
                        color: var(--brand-color-secondary-neutral-charcoal);
                        line-height: 24px;
                    }

                    .signage-btn {
                        a {
                            margin: 0;
                            padding: 0;
                            position: relative;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            width: 220px;
                            height: 50px;
                            font-size: var(--brand-font-size-0-9);
                            font-weight: var(--brand-font-weight-normal);
                            color: var(--bs-white);
                            gap: 0 5px;
                            text-transform: uppercase;
                            background: var(--brand-color-tertiary-dark-teal);
                            border-radius: 8px;
                            letter-spacing: 1px;
                        }
                    }
                }

                .signage-right {
                    position: relative;
                    flex: 1;

                    img {
                        width: 100%;
                    }
                }
            }

            .list-sec {
                position: relative;
                gap: 0 4%;

                .list-box {
                    position: relative;
                    flex: 1;

                    .list-title {
                        margin: 0 0 22px 0;
                        padding: 0;
                        position: relative;
                        line-height: 32px;
                        font-size: var(--brand-font-size-22);
                        color: var(--brand-color-tertiary-dark-teal);
                        font-weight: var(--brand-font-weight-medium);
                    }

                    ul {
                        position: relative;
                        gap: 15px 0;

                        a {
                            position: relative;
                            display: flex;
                            gap: 0 8px;
                            color: #000;

                            .material-symbols-outlined {
                                font-weight: var(--brand-font-weight-bolder);
                                color: var(--brand-color-primary);
                            }

                            .list-text {
                                margin: 0;
                                padding: 0;
                                position: relative;
                                font-family: var(--brand-font-family-secondary);
                                font-size: var(--brand-font-size-1);
                                color: var(--brand-color-secondary-neutral-charcoal);
                                line-height: 22px;

                                .date {
                                    display: flex;
                                    margin: 0;
                                    padding: 0;
                                    position: relative;
                                    line-height: 18px;
                                    font-size: var(--brand-font-size-0-875);
                                    color: var(--brand-color-secondary-neutral-charcoal);
                                    font-weight: var(--brand-font-weight-bold);
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}

/*----SUBMIT A PIZZA SEC----*/

/*-------MEDIA SCREEN 1440px-------*/
@media only screen and (max-width: 1440px) {
    .submit-a-pizza-sec {
        .submit-a-pizza-body {
            max-width: 1200px;
            gap: 50px 0;

            .submit-a-pizza-work {
                gap: 20px 0;

                .submit-a-pizza-btn {
                    margin: 30px auto 0 auto;
                    gap: 0 25px;
                    max-width: 900px;
                    width: 100%;

                    a {
                        padding: 0 16px;
                        font-size: var(--brand-font-size-0-875);
                        letter-spacing: 1px;
                    }
                }
            }
        }
    }
}

/*-------MEDIA SCREEN 1440px-------*/

/*-------MEDIA SCREEN 768px-------*/
@media only screen and (max-width: 768px) {
    .submit-a-pizza-sec {
        padding: 50px 0;
        background: url(/assets/images/background-img.jpg) center center no-repeat;
        height: 100%;

        .submit-a-pizza-body {
            .submit-a-pizza-work {
                .submit-a-pizza-logo {
                    max-width: 180px;
                    margin: 0 auto;
                    position: relative;
                }

                .submit-a-pizza-btn {
                    a {
                        line-height: 16px;
                        width: 100%;
                    }
                }
            }
        }
    }
}

/*-------MEDIA SCREEN 768px-------*/

/*-------MEDIA SCREEN 576px-------*/
@media only screen and (max-width: 576px) {
    .submit-a-pizza-sec {
        .submit-a-pizza-body {
            .submit-a-pizza-work {
                .submit-a-pizza-title {
                    line-height: 32px;
                    font-size: var(--snjy-font-size-3);
                }

                .submit-a-pizza-desc {
                    line-height: 22px;
                    font-size: var(--brand-font-size-1);
                }

                .submit-a-pizza-btn {
                    flex-direction: column;
                    gap: 20px;
                    
                    a {
                        width: 100%;
                    }
                }
            }
        }
    }
}

/*-------MEDIA SCREEN 576px-------*/