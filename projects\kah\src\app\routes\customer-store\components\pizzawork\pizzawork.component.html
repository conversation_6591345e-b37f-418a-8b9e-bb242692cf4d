<section class="webminar-sec">   
    <owl-carousel-o [options]="customOptions">
        <ng-template carouselSlide>
            <div class="webminar-body">
                <div class="webminar-img">
                    <img src="/assets/images/webminar-img-1.jpg" alt="" />
                </div>
                <div class="webminar-content">
                    <div class="webminar-date"><span class="material-symbols-outlined">calendar_month</span> 05/07/2024</div>
                    <div class="webminar-title">Webminar Title Name Here</div>
                    <div class="webminar-desc">Sed ut perspiciatis unde omnis iste natus error sit voluptatem accusantium doloremque laudantium, totam rem aperiam</div>
                    <a href="">Learn More <span class="material-symbols-outlined">east</span></a>
                </div>
            </div>
        </ng-template>
      </owl-carousel-o>
</section>

<section class="submit-a-pizza-sec">
    <div class="submit-a-pizza-body submit-a-pizza-body d-flex flex-column">
        <div class="submit-a-pizza-work">
            <div class="submit-a-pizza-logo">
                <img [src]="data?.pizza_w_logo.data.attributes.url" alt="" />
            </div>
            <div class="submit-a-pizza-title d-flex justify-content-center">{{data?.pizza_w_title}}</div>
            <div class="submit-a-pizza-desc">{{data?.pizza_w_p}}</div>
            <div class="submit-a-pizza-btn d-flex align-items-center justify-content-center">
                <ng-container *ngFor="let pizzaWorkbtnlist of data?.Pizza_Work_Button_List">
                    <a [href]="[pizzaWorkbtnlist?.pizza_work_button_links]" target="_blank">
                        {{pizzaWorkbtnlist?.pizza_work_button_text}}
                        <span class="material-symbols-outlined">{{pizzaWorkbtnlist?.pizza_work_button_icon}}</span>
                    </a>
                </ng-container>
            </div>
        </div>
        <!-- <div class="signage-and-list d-flex justify-content-between">
            <div class="signage-sec d-flex align-items-center">
                <div class="signage-left">
                    <div class="signage-title">{{signUpRenewelData?.signage_renewal_title}}</div>
                    <div class="signage-p">{{signUpRenewelData?.signage_renewal_p}}</div>
                    <div class="signage-btn">
                        <ng-container *ngIf="signUpRenewelData?.signage_renewal_link_url">
                            <a [href]="[signUpRenewelData?.signage_renewal_link_url]" target="_blank">
                                {{signUpRenewelData?.signage_renewal_link_name}}
                                <span class="material-symbols-outlined">
                                    {{signUpRenewelData?.signage_renewal_link_icon}}
                                </span>
                            </a>
                        </ng-container>
                    </div>
                </div>
                <div class="signage-right">
                    <img [src]="signUpRenewelData?.signage_renewal_image.data.attributes.url" alt="">
                </div>
            </div>
            <div class="list-sec d-flex justify-content-between">
                <div class="list-box" *ngFor="let item of watchTheReplay">
                    <div class="list-title">{{item?.sidebar_list_title}}</div>
                    <ul class="d-flex flex-column">
                        <ng-container *ngFor="let sidebarlist of item?.sidebar_list">
                            <li *ngIf="sidebarlist?.sidebar_list_link">
                                <a [href]="[sidebarlist?.sidebar_list_link]" target="_blank">
                                    <span class="material-symbols-outlined">{{sidebarlist?.sidebar_list_icon}}</span>
                                    <span class="list-text">
                                        {{sidebarlist?.sidebar_list_name}}
                                        <span class="date">{{sidebarlist?.sidebar_list_date}}</span>
                                    </span>
                                </a>
                            </li>
                        </ng-container>
                    </ul>
                </div>
            </div>
        </div> -->
    </div>
</section>

