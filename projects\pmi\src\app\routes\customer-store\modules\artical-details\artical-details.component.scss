/*-------<PERSON><PERSON><PERSON><PERSON><PERSON>GE ARTICLE DETAILS-------*/
.article-details-banner-sec {
    margin: 0;
    padding: 0;
    overflow: hidden;
    z-index: 0;

    .article-details-banner-body {
        margin: 0 auto;
        padding: 25px 30px;
        max-width: 1500px;
        height: 100%;
        width: 100%;
        gap: 25px 0;

        .bedcrumbs {
            font-weight: var(--brand-font-weight-semi-bold);
            font-size: var(--brand-font-size-0-875);
            color: var(--brand-color-dark-secondary);
            line-height: 12px;

            span {
                color: var(--brand-color-dark-600);
            }
        }
    }
}

.main-title-sec {
    margin: 0;
    padding: 20px 0;

    .main-title-body {
        margin: 0 auto;
        padding: 0 30px;
        max-width: 1400px;
        width: 100%;
        gap: 0 2rem;
        justify-content: space-between;

        .main-title-left {
            margin: 0;
            padding: 0;
            gap: 24px 0;
            max-width: 70%;
            width: 100%;

            .main-title {
                margin: 0;
                padding: 0;
                font-size: var(--brand-font-size-4);
                font-weight: var(--brand-font-weight-semi-bold);
                line-height: 42px;
            }

            .main-desc {
                margin: 0 0 30px 0;
                padding: 0;
                font-size: var(--brand-font-size-1);
                color: var(--brand-color-dark-600);
            }

            .main-title-footer {
                gap: 20px 0;

                .main-title-calendar {
                    gap: 0 8px;
                    font-size: var(--brand-font-size-1);
                    color: var(--brand-color-dark-600);

                    span {
                        font-size: 22px;
                        color: var(--brand-color-secondary-accent);
                    }
                }

                .category-list {
                    gap: 0 15px;
                    position: relative;
                    font-size: var(--brand-font-size-1);
                    letter-spacing: 0.25px;
                    color: var(--brand-color-dark-600);

                    .category-tag {
                        position: relative;
                        display: flex;
                        width: 80px;
                        color: var(--brand-color-black);
                    }

                    .category-name {
                        position: relative;
                        margin: 0 10px 0 0;

                        &:before {
                            position: absolute;
                            content: '';
                            top: 7px;
                            right: -15px;
                            width: 2px;
                            height: 14px;
                            background: var(--brand-scroll-thumb-color);
                        }

                        &:last-child:before {
                            display: none;
                        }
                    }

                }

            }
        }

        .main-title-right {
            gap: 20px 0;

            .add-favorite-btn {
                position: relative;
                margin: 0;

                input {
                    display: none;
                }

                label {
                    margin: 0;
                    padding: 8px 12px;
                    gap: 0 8px;
                    font-size: var(--brand-font-size-0-9);
                    color: var(--brand-color-dark-600);
                    height: 48px;
                    justify-content: center;
                    border-radius: 8px;
                    border: 1px solid;
                    cursor: pointer;
                    transition: all 0.3s ease-in-out;
                }

                input[type=checkbox]:checked+label {
                    color: #FFF;
                    background: var(--brand-color-primary);
                    border-color: var(--brand-color-primary);
                }
            }

            .latest-views {
                gap: 0 8px;
                font-size: var(--brand-font-size-1);
                letter-spacing: 0.25px;
                color: var(--brand-color-dark-600);

                span {
                    color: var(--brand-color-primary);
                }
            }
        }
    }
}

.article-content-sec {
    margin: 10px 0 0 0;
    padding: 0 0 40px 0;
    background: #F9F6F5;

    .article-content-body {
        margin: 0 auto;
        padding: 0 30px;
        max-width: 1400px;
        display: flex;
        flex-wrap: wrap;
        align-items: start;
        justify-content: space-between;

        .article-content-title {
            margin: 0 0 20px 0;
            height: 72px;
            font-size: var(--brand-font-size-22);
            color: var(--brand-color-primary);
            font-weight: var(--brand-font-weight-semi-bold);
            border-bottom: 1px solid var(--brand-accordian-button-color);
            width: 100%;
        }

        .article-content-details {
            margin: 0;
            max-width: 60%;
            width: 100%;

            h2 {
                margin: 0 0 12px 0;
                font-size: var(--brand-font-size-2);
                font-weight: var(--brand-font-weight-semi-bold);
                color: var(--brand-color-secondary-neutral-charcoal);
            }

            .article-details-html {
                margin: 0;
                padding: 0;
                font-size: var(--brand-font-size-1);
                color: var(--brand-color-dark-600);

                p {
                    word-wrap: break-word;
                    line-height: 26px;
                }
            }
        }

        .article-media {
            margin: 0 0 40px 0;

            .article-media-video {
                width: 100%;
                max-width: 830px;

                video {
                    width: 100%;
                    position: relative;
                    border-radius: 16px;
                }
            }
        }

        .media-pdf {
            margin: 0;
            padding: 0;
            grid-template-columns: repeat(auto-fill, minmax(170px, 1fr));
            gap: 24px;
            width: 100%;
            max-width: 35%;

            .media-pdf-download {
                margin: 0;
                padding: 14px 0 0 0;
                position: relative;
                width: 100%;
                height: 110px;
                display: flex;
                flex-direction: column;
                align-items: center;
                gap: 8px 0;
                background: var(--brand-color-white);
                border-radius: 8px;
                box-shadow: 3px 3px 10px rgba(0, 0, 0, 0.0334);
                overflow: hidden;

                span.material-symbols-outlined {
                    color: var(--brand-color-primary);
                    font-size: 28px;
                }

                span.pdf-text {
                    width: 100%;
                    font-size: var(--brand-font-size-0-9);
                    line-height: 18px;
                    color: var(--brand-color-dark-600);
                    font-weight: 600;
                }

                span.pdf-download {
                    margin: auto 0 0 0;
                    padding: 0;
                    position: relative;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    width: 100%;
                    gap: 0;
                    height: 24px;
                    font-size: 12px;
                    color: #000;
                    background: #FFEAC2;

                    span.material-symbols-outlined {
                        font-size: 18px;
                        color: #000;
                    }
                }
            }
        }

        .date-format {
            grid-template-columns: repeat(auto-fill, minmax(210px, 1fr));
            gap: 0 24px;
            width: 100%;
            max-width: 830px;
            display: none !important;

            .date-format-box {
                margin: 0;
                padding: 18px;
                position: relative;
                display: flex;
                flex-direction: column;
                gap: 10px 0;
                width: 100%;
                height: 124px;
                background: #FFF;
                border-radius: 8px;
                box-shadow: 0 0 7px #00000005;
                overflow: hidden;

                .date-format-title {
                    margin: 0 0 5px 0;
                    padding: 0 0 5px 0;
                    display: flex;
                    font-size: var(--brand-font-size-0-9);
                    line-height: 18px;
                    color: var(--brand-color-secondary-neutral-charcoal);
                    font-weight: 600;
                    border-bottom: 1px solid var(--brand-color-light-border);
                }

                .date {
                    gap: 0 6px;
                    font-size: var(--brand-font-size-0-8);
                    letter-spacing: 0.25px;
                    color: var(--brand-color-dark-600);
                    line-height: 21px;

                    span {
                        color: var(--brand-color-primary);
                        font-size: 20px;
                    }
                }
            }
        }

    }
}

.banner-slider-sec {
    margin: 0;
    padding: 0;
    position: relative;
    width: 100%;
    border-radius: 24px;
    overflow: hidden;
    height: 460px;
}


/*-------KNOWLWDGE ARTICLE DETAILS-------*/
.banner-slider-sec img {
    border-radius: 24px;
    width: 100%;
}

/*-------MEDIA SCREEN 1440px-------*/
@media only screen and (max-width: 1440px) {
    .article-details-banner-sec {
        .article-details-banner-body {
            max-width: 1200px;

            .banner-slider-sec {
                height: 420px;
            }
        }
    }

    .main-title-sec {
        .main-title-body {
            max-width: 1200px;

            .main-title-left {
                .main-title {
                    font-size: var(--brand-font-size-3);
                    line-height: 36px;
                }

                .main-desc {
                    margin: 0;
                }
            }
        }
    }

    .article-content-sec {
        .article-content-body {
            max-width: 1200px;
        }
    }
}

/*-------MEDIA SCREEN 1440px-------*/

/*-------MEDIA SCREEN 1024px-------*/
@media only screen and (max-width: 1024px) {
    .article-content-sec {
        .article-content-body {
            .article-content-details {
                margin: 0 0 30px 0;
                max-width: 100%;
                width: 100%;
            }

            .media-pdf {
                width: 100%;
                max-width: 100%;
            }
        }
    }
}

/*-------MEDIA SCREEN 1024px-------*/

/*-------MEDIA SCREEN 768px-------*/
@media only screen and (max-width: 768px) {
    .article-details-banner-sec {
        .article-details-banner-body {
            .banner-slider-sec {
                height: 320px;
            }
        }
    }

    .main-title-sec {
        .main-title-body {
            .main-title-left {
                .main-title {
                    font-size: var(--brand-font-size-2);
                    line-height: 26px;
                    font-weight: var(--brand-font-weight-medium);
                }
            }
        }
    }
}

/*-------MEDIA SCREEN 768px-------*/

/*-------MEDIA SCREEN 576px-------*/
@media only screen and (max-width: 576px) {
    .article-details-banner-sec {
        .article-details-banner-body {
            .banner-slider-sec {
                height: fit-content;
            }
        }
    }

    .main-title-sec {
        .main-title-body {
            flex-direction: column-reverse;
            gap: 20px;

            .main-title-left {
                gap: 18px 0;
                max-width: 100%;

                .main-title {
                    font-size: var(--brand-font-size-2);
                    line-height: 26px;
                    font-weight: var(--brand-font-weight-medium);
                }
            }

            .main-title-right {
                gap: 20px;
                flex-direction: row !important;
            }
        }
    }

    .article-content-sec {
        margin: 0;
    }
}

:host::ng-deep {

    .table thead th,
    .table thead th td,
    .table,
    .table tbody tr td,
    .table tbody tr {
        background-color: transparent !important;
    }

    pre {
        background: hsla(0, 0%, 78%, .3);
        border: 1px solid #c4c4c4;
        border-radius: 2px;
        color: #353535;
        direction: ltr;
        font-style: normal;
        min-width: 200px;
        padding: 1em;
        tab-size: 4;
        text-align: left;
        white-space: pre-wrap;
    }
}