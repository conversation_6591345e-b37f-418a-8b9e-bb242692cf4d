/*----CATEGORY AND SIDEBAR----*/
.category-and-sidebar-sec {
    margin: 0;
    padding: 60px 0;
    position: relative;
    background: linear-gradient(359deg, #FFF, #ffeede, #FFF);

    .category-and-sidebar-body {
        margin: 0 auto;
        padding: 0 30px;
        position: relative;
        max-width: 1500px;
        width: 100%;
        display: flex;
        gap: 0 4%;
        align-items: self-start;

        .category-part {
            margin: 0;
            padding: 0;
            position: relative;
            flex: 4;
            width: 100%;

            h2 {
                margin: 0 0 60px 0;
                padding: 0;
                position: relative;
                font-size: var(--brand-font-size-4);
                font-weight: var(--brand-font-weight-semi-bold);
                color: var(--brand-color-black) !important;
                line-height: 22px;
            }

            .category-list {
                margin: 0;
                padding: 0;
                position: relative;
                display: grid;
                grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
                gap: 30px;

                .category-item {
                    margin: 0;
                    padding: 1px;
                    position: relative;
                    width: 100%;
                    background: #ffffff;
                    border-radius: 12px;
                    box-shadow: 0 3px 3px rgb(0 0 0 / 7%);
                    overflow: hidden;
                    transition: all 0.3s ease-in-out;

                    &:before {
                        content: "";
                        position: absolute;
                        top: 0;
                        right: 0;
                        bottom: 0;
                        left: 0;
                        width: 100%;
                        height: 100%;
                        z-index: 0;
                        border-radius: inherit;
                        background: linear-gradient(to bottom, #14728A, #000);
                    }

                    &:nth-child(2):before {
                        background: linear-gradient(to bottom, #F3470B, #000);
                    }

                    &:nth-child(3):before {
                        background: linear-gradient(to bottom, #4D7BF4, #000);
                    }

                    &:nth-child(4):before {
                        background: linear-gradient(to bottom, #7C24AE, #000);
                    }

                    &:nth-child(5):before {
                        background: linear-gradient(to bottom, #8CA54A, #000);
                    }

                    &:nth-child(6):before {
                        background: linear-gradient(to bottom, #A4A4A4, #000);
                    }

                    .c-item-body {
                        margin: 0;
                        padding: 24px;
                        position: relative;
                        display: flex;
                        align-items: center;
                        flex-direction: column;
                        gap: 10px;
                        border-radius: 12px;
                        background: #fff;
                        z-index: 1;
                        height: 100%;

                        .c-item-icon {
                            margin: 0;
                            padding: 0;
                            position: relative;
                            max-width: 220px;

                            img {
                                width: 100%;
                            }
                        }

                        .c-item-title {
                            margin: 20px 0;
                            padding: 0;
                            position: relative;
                            font-size: var(--brand-font-size-2);
                            font-weight: var(--brand-font-weight-semi-bold);
                            color: var(--brand-color-black);
                            line-height: 22px;
                            transition: all 0.3s ease-in-out;
                        }

                        .c-item-p {
                            margin: 0 auto 20px auto;
                            padding: 0;
                            position: relative;
                            font-family: var(--brand-font-family-secondary);
                            font-size: var(--brand-font-size-0-9);
                            color: var(--brand-color-dark-600);
                            line-height: 20px;
                            text-align: center;
                            max-width: 300px;
                        }

                        .c-item-link {
                            a {
                                margin: 0;
                                padding: 0;
                                position: relative;
                                display: flex;
                                align-items: center;
                                height: 14px;
                                font-size: var(--brand-font-size-0-875);
                                font-weight: var(--brand-font-weight-semi-bold);
                                color: var(--brand-color-primary);
                                gap: 0 5px;
                                text-transform: uppercase;

                                span {
                                    font-size: 20px;
                                }
                            }

                        }

                    }


                    &:hover {
                        box-shadow: 0 7px 15px rgb(0 0 0 / 10%);
                    }
                }
            }
        }

        .sidebar-part {
            margin: 0;
            padding: 70px 0 0 0;
            flex: 2;
            width: 100%;
            position: sticky;
            top: 50px;

            .home-sidebar-box {
                margin: 0 0 32px 0;
                padding: 0;
                position: relative;
                min-height: 180px;

                .home-sidebar-box-title {
                    margin: 0 0 20px 0;
                    padding: 0 0 20px 0;
                    position: relative;
                    display: flex;
                    align-items: center;
                    font-size: 22px;
                    font-weight: 600;
                    color: #003560;
                    border-bottom: 1px solid #d2d7e1;
                }

                .home-sidebar-box-list {
                    margin: 0;
                    padding: 0;
                    position: relative;

                    ul {
                        margin: 0;
                        padding: 0;
                        position: relative;
                        display: flex;
                        flex-direction: column;
                        gap: 15px 0;

                        li {
                            margin: 0;
                            padding: 0 0 0 24px;
                            position: relative;

                            a {
                                display: flex;
                                align-items: center;
                                justify-content: space-between;
                                gap: 0 5px;

                                .material-symbols-outlined {
                                    font-size: 20px;
                                    color: #ff7800;
                                    position: absolute;
                                    left: 0;
                                }

                                span.hsb-text {
                                    margin: 0;
                                    padding: 0;
                                    position: relative;
                                    display: flex;
                                    align-items: center;
                                    font-size: 15px;
                                    line-height: 15px;
                                    color: #5b5d6b;
                                    font-family: "Inter Tight", sans-serif;
                                    font-weight: 500;
                                    text-align: left;
                                    width: 70%;
                                }

                                span.hsb-date {
                                    margin: 0;
                                    padding: 2px;
                                    display: flex;
                                    align-items: center;
                                    justify-content: center;
                                    height: 22px;
                                    width: 88px;
                                    font-family: 'Inter Tight', sans-serif;
                                    font-size: 12px;
                                    line-height: 13px;
                                    font-weight: 500;
                                    color: #000;
                                    border-radius: 40px;
                                }

                                &:hover span.hsb-text {
                                    text-decoration: underline;
                                }

                            }

                            &:nth-child(odd) a span.hsb-date {
                                background: #ff8a008a;
                            }

                            &:nth-child(even) a span.hsb-date {
                                background: var(--brand-color-tertiary-dark-teal);
                                color: var(--bs-white);
                            }

                        }
                    }
                }
            }

            .hot-t {
                span.hsb-text {
                    width: 100% !important;
                }
            }
        }
    }
}

/*----CATEGORY AND SIDEBAR----*/