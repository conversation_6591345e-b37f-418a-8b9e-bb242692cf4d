/*--------<PERSON><PERSON> FEED CSS-------*/
.newsfeed-sec {
    margin: 0;
    padding: 60px 0;
    position: relative;

    .news-feed-body {
        margin: 0 auto;
        padding: 0 30px;
        max-width: 1500px;
        width: 100%;
        position: relative;

        .newsfeed-title-sec {
            margin: 0 0 60px 0;

            .news-feed-title {
                flex: 1;
                font-size: var(--brand-font-size-4);
                font-weight: var(--brand-font-weight-bold);
                color: var(--brand-color-deep-nevy-blue);
            }

            .news-feed-desc {
                flex: 1;
                font-size: var(--brand-font-size-1);
                color: var(--brand-color-dark-600);
            }
        }

        .newdfeed-list {
            grid-template-columns: repeat(auto-fill, minmax(260px, 1fr));
            gap: 30px;

            .newsfeed-box {
                position: relative;

                a {
                    padding: 12px;
                    position: relative;
                    display: flex;
                    flex-direction: column;
                    gap: 20px 0;
                    border: 1px solid #dae2f5;
                    border-radius: 12px;
                    box-shadow: 0 4px 4px #ececec;
                    text-align: center;

                    .news-f-img {
                        position: relative;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        overflow: hidden;
                        height: 210px;
                        border-radius: 8px;

                        img {
                            width: 100%;
                            height: 100%;
                            object-fit: cover;
                            transform: scale(1);
                            transition: all 0.3s ease-in-out;
                        }
                    }

                    &:hover .news-f-img {

                        img {
                            transform: scale(1.2);
                        }
                    }

                    .news-f-cnt {
                        gap: 8px;

                        .news-f-box-title {
                            font-size: var(--brand-font-size-1);
                            font-weight: var(--brand-font-weight-bold);
                            color: var(--brand-color-secondary-neutral-charcoal);
                        }

                        .news-f-link {
                            font-size: var(--brand-font-size-0-8);
                            gap: 0 4px;
                            font-weight: var(--brand-font-weight-semi-bold);
                            color: var(--brand-color-primary);
                            text-transform: uppercase;

                            span {
                                font-size: var(--brand-font-size-22);
                            }
                        }
                    }
                }
            }
        }

    }
}

/*--------NEWS FEED CSS-------*/

/*-------MEDIA SCREEN 576px-------*/
@media only screen and (max-width: 768px) {
    .newsfeed-sec {
        padding: 40px 0;

        .news-feed-body {
            .newsfeed-title-sec {
                .news-feed-title {
                    font-size: var(--brand-font-size-3);
                }

                margin: 0 0 40px 0;
                flex-direction: column;
                gap: 20px;
            }
        }
    }
}

/*-------MEDIA SCREEN 576px-------*/