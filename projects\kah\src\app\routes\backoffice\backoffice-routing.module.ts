import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';

const routes: Routes = [
  {
    path: 'customer',
    loadChildren: () => import('ng-snjya').then((m) => m.CustomerModule),
  },
  {
    path: 'product',
    loadChildren: () => import('ng-snjya').then((m) => m.ProductsModule),
  },
  {
    path: 'product-register',
    loadChildren: () => import('ng-snjya').then((m) => m.ProductRegisterModule),
  },
  {
    path: 'employees',
    loadChildren: () => import('ng-snjya').then((m) => m.EmployeesModule),
  },
  {
    path: 'configs',
    loadChildren: () => import('ng-snjya').then((m) => m.ConfigModule),
  },
  {
    path: 'admin',
    loadChildren: () => import('ng-snjya').then((m) => m.AdministrationModule),
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class BackOfficeRoutingModule {}
