import { Injectable } from '@angular/core';
import {
  ActivatedRouteSnapshot,
  CanActivate,
  CanActivateChild,
  Router,
  RouterStateSnapshot,
  UrlTree,
} from '@angular/router';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { AskPermissionComponent } from '../../routes/customer-store/components/ask-permission/ask-permission.component';
import { TicketsService } from '../../routes/customer-store/modules/my-activation/my-activation.service';
import { lastValueFrom } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class ActivationRequestGuard implements CanActivate, CanActivateChild {
  constructor(
    private modalService: NgbModal,
    private service: TicketsService,
    private router: Router,
  ) { }

  async canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Promise<boolean | UrlTree | any> {
    return await this.authenticate(route, state.url, false);
  }

  async canActivateChild(
    childRoute: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Promise<boolean | UrlTree | any> {
    return await this.authenticate(childRoute, state.url, true);
  }

  private async authenticate(
    route: ActivatedRouteSnapshot,
    url: string,
    is_child_route: boolean
  ): Promise<boolean | UrlTree | any> {
    if (url.includes('campaign-center') || url.includes('my-activation')) {
      const hasPermission = await lastValueFrom(this.service.hasPermission());
      if (hasPermission) {
        return true;
      } else {
        this.showPermissionDeniedModal();
        return this.router.parseUrl(`/store`);;
      }
    }
    return true;
  }

  showPermissionDeniedModal() {
    const modalRef = this.modalService.open(AskPermissionComponent, {
      modalDialogClass: 'permission-denied-modal',
    });
  }
}
