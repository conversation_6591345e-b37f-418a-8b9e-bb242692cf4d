.all-main-title-sec {
    margin: 0;
    padding: 30px 0 22px 0;
    opacity: revert;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    gap: 20px 0;

    .product-details-main-title {
        align-items: flex-start;
        justify-content: flex-start;
        text-align: left;
        padding: 0 0 25px 0;
    }

    h1 {
        margin: 0 0;
        padding: 0;
        position: relative;
        font-size: var(--snjy-font-size-3);
        font-weight: var(--snjy-font-weight-bold);
        color: var(--snjy-button-color-primary);
        line-height: 23px;
    }

    .all-bedcrumbs {
        margin: 0;
        padding: 0;
        position: relative;

        ul {
            margin: 0;
            padding: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            list-style: none;
            gap: 0 4px;

            li {
                margin: 0;
                padding: 0 0 0 30px;
                font-size: var(--snjy-font-size-0-875);
                display: flex;
                align-items: center;
                justify-content: center;
                position: relative;
                color: var(--snjy-color-dark-secondary);
                font-weight: var(--snjy-font-weight-medium);

                &:before {
                    position: absolute;
                    content: '\e5c8';
                    font-family: 'Material Icons Outlined';
                    top: 0;
                    left: 5px;
                    bottom: 0;
                    margin: auto 0;
                    font-size: var(--snjy-font-size-1-125);
                    color: var(--snjy-color-dark-secondary);
                    font-weight: var(--snjy-font-weight-normal);
                    line-height: 23px;
                }

                &:first-child {
                    padding: 0;

                    &:before {
                        display: none;
                    }
                }

                a {
                    margin: 0;
                    padding: 0;
                    display: inline-flex;
                    align-items: center;
                    justify-content: center;
                    line-height: 20px;
                    gap: 0 2px;
                    color: var(--snjy-color-text-ternary);
                    font-weight: var(--snjy-font-weight-normal);

                    span:not(.material-icons-outlined) {
                        font-size: 22px;
                        font-variation-settings: 'FILL' 0, 'wght' 400, 'GRAD' 0, 'opsz' 40;
                    }
                }
            }
        }
    }
}