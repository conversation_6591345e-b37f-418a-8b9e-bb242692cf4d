import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { RouterModule } from "@angular/router";
import { LoadingBarComponent } from "./components/loading-bar/loading-bar.component";
import { NavComponent } from "../routes/customer-store/components/nav/nav.component";
import { FooterComponent } from "../routes/customer-store/components/footer/footer.component";
import { NgbDropdownModule } from "@ng-bootstrap/ng-bootstrap";

const MODULES: any[] = [
  CommonModule,
  RouterModule,

];
@NgModule({
  declarations: [
    LoadingBarComponent,
    NavComponent, FooterComponent
  ],
  imports: [
    NgbDropdownModule,
    ...MODULES,
  ],
  exports: [
    ...MODULES,
    LoadingBarComponent,
    NavComponent, FooterComponent
  ],
})
export class SharedModule { }
