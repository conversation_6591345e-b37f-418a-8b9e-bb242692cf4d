import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { NgbDropdownModule, NgbTooltipModule, } from "@ng-bootstrap/ng-bootstrap";

import { CustomerStoreRoutingModule } from "./customer-store-routing.module";
import { CustomerStoreComponent } from "./customer-store.component";
import { SharedModule } from "../../shared/shared.module";
import { NavComponent } from "./components/nav/nav.component";
import { FooterComponent } from "./components/footer/footer.component";

@NgModule({
  declarations: [CustomerStoreComponent, NavComponent, FooterComponent],
  imports: [
    CommonModule,
    SharedModule,
    CustomerStoreRoutingModule,
    NgbDropdownModule,
    NgbTooltipModule,
  ],
})
export class CustomerStoreModule { }
