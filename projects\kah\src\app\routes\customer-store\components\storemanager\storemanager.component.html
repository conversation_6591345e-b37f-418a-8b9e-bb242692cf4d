<ng-container *ngIf="data?.Kah_Link_Box_List.length">
    <section class="kah-list-sec position-relative">
        <div class="kah-list-body position-relative">
            <div class="kah-list d-grid position-relative">
                <ng-container *ngFor="let kah_box_list of data?.Kah_Link_Box_List">
                    <div class="kah-list-box">
                        <div class="kah-list-title d-flex position-relative">
                            <span class="material-symbols-outlined">{{kah_box_list?.link_box_icon}}</span>
                            {{kah_box_list?.Kah_Link_Box_Title}}
                        </div>
                        <ul class="d-flex position-relative flex-column">
                            <ng-container *ngFor="let kah_box_link_list of kah_box_list?.Kah_Link_Box">
                                <li>
                                    <a [href]="[kah_box_link_list?.Kah_Link_Box_Url]" target="_blank">
                                        <span
                                            class="material-symbols-outlined">{{kah_box_link_list?.<PERSON>h_Link_Box_Icon}}</span>
                                        <span class="kah-list-text">{{kah_box_link_list?.Kah_Link_Box_Text}}</span>
                                    </a>
                                </li>
                            </ng-container>
                        </ul>
                    </div>
                </ng-container>
            </div>
        </div>
    </section>
</ng-container>