footer.footer-sec {
    margin: 0;
    padding: 0;
    position: relative;
    background: var(--brand-footer-bg);

    .footer-body {
        margin: 0;
        padding: 0;
        position: relative;

        .footer-top {
            margin: 0 auto;
            padding: 40px 30px;
            max-width: 1500px;
            gap: 30px 0;

            .ftr-logo-box {
                width: 200px;
            }

            .back-top {
                a {
                    margin: 0;
                    padding: 0 20px;
                    position: relative;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    width: fit-content;
                    height: 44px;
                    font-size: var(--brand-font-size-0-9);
                    font-weight: var(--brand-font-weight-normal);
                    color: var(--brand-grey);
                    gap: 0 15px;
                    text-transform: uppercase;
                    background: var(--bs-white);
                    border-radius: 8px;
                    border: 1px solid var(--brand-black-10);
                }
            }

            .ftr-social-media-list {
                ul {
                    gap: 0 15px;

                    a {
                        margin: 0;
                        padding: 0 15px;
                        position: relative;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        gap: 0 8px;
                        height: 40px;
                        min-width: 130px;
                        font-size: var(--brand-font-size-0-875);
                        font-family: var(--brand-font-family-primary);
                        font-weight: var(--brand-font-weight-normal);
                        color: var(--bs-black);
                        letter-spacing: 0.2px;
                        border: 1px solid var(--brand-black-10);
                        border-radius: 50px;

                        img {
                            width: 22px;
                            position: relative;
                            top: 1px;
                        }
                    }
                }
            }

            .ftr-top-links {
                position: relative;
                flex-direction: column;
                gap: 20px 0;

                .ftr-top-link-box {
                    margin: 0;
                    position: relative;
                    flex-direction: column;
                    padding: 20px;
                    border-radius: 8px;
                    background: var(--brand-color-white-40);
                    border: 1px solid var(--border-black-05);

                    .ftr-top-link-title {
                        margin: 0 0 15px 0;
                        padding: 0;
                        position: relative;
                        line-height: 28px;
                        font-size: var(--brand-font-size-1-125);
                        color: var(--brand-color-tertiary-dark-teal);
                        font-weight: var(--brand-font-weight-medium);
                    }

                    ul {
                        margin: 0;
                        padding: 0;
                        position: relative;
                        display: flex;
                        gap: 25px;

                        a {
                            position: relative;
                            font-weight: var(--brand-font-weight-semi-bold);
                            font-family: var(--brand-font-family-secondary);
                            font-size: var(--brand-font-size-0-875);
                            color: var(--brand-color-secondary-neutral-charcoal);
                            line-height: 22px;
                            display: flex;

                            &:hover {
                                text-decoration: underline !important;
                            }
                        }
                    }
                }
            }
        }

        .footer-line {
            margin: 0;
            padding: 0;
            position: relative;
            display: flex;
            width: 100%;
            height: 1px;
            background: var(--brand-black-10);
        }

        .footer-bottom {
            margin: 0 auto;
            padding: 15px 30px;
            max-width: 1500px;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: space-between;
            height: 80px;

            .ftr-copy-right {
                margin: 0;
                padding: 0;
                position: relative;
                font-size: var(--brand-font-size-0-875);
                color: var(--brand-color-secondary-neutral-charcoal);
                font-weight: var(--brand-font-weight-normal);
                line-height: 15px;
                text-align: left;
            }

            .ftr-bottom-links ul {
                margin: 0;
                padding: 0;
                position: relative;
                display: flex;
                align-items: center;
                justify-content: flex-end;
                gap: 30px;

                a {
                    margin: 0;
                    padding: 0;
                    position: relative;
                    font-size: var(--brand-font-size-0-875);
                    color: var(--brand-color-secondary-neutral-charcoal);
                    font-weight: var(--brand-font-weight-normal);
                    line-height: 15px;
                    text-align: left;
                    text-transform: capitalize;
                }
            }
        }
    }
}

/*-------MEDIA SCREEN 1440px-------*/
@media only screen and (max-width: 1440px) {
    footer.footer-sec {
        .footer-body {
            .footer-top {
                max-width: 1200px;

                .ftr-logo-box {
                    width: 160px;
                }

                .back-top {
                    a {
                        padding: 0 12px;
                        height: 42px;
                        font-size: var(--brand-font-size-0-875);
                        gap: 0 10px;
                    }
                }

                .ftr-social-media-list {
                    ul {
                        gap: 15px;
                        flex-wrap: wrap;

                        li {
                            flex: 0 0 100px;

                            a {
                                min-width: fit-content;
                            }
                        }
                    }
                }
            }

            .footer-bottom {
                padding: 30px 30px;
                max-width: 1200px;
                height: fit-content;
            }
        }
    }
}

/*-------MEDIA SCREEN 1440px-------*/

/*-------MEDIA SCREEN 768px-------*/
@media only screen and (max-width: 768px) {
    footer.footer-sec {
        .footer-body {
            .footer-top {
                .ftr-logo-box {
                    width: 180px;
                }
            }
        }
    }
}

/*-------MEDIA SCREEN 768px-------*/

/*-------MEDIA SCREEN 576px-------*/
@media only screen and (max-width: 576px) {
    footer.footer-sec {
        .footer-body {
            .footer-top {
                .ftr-logo-box {
                    width: 130px;
                }

                .back-top {
                    a {
                        padding: 0 8px;
                        font-size: 0;
                        gap: 0;
                        width: 42px;
                    }
                }
            }

            .footer-bottom {
                flex-direction: column-reverse;
                gap: 20px;
            }
        }
    }
}

/*-------MEDIA SCREEN 576px-------*/