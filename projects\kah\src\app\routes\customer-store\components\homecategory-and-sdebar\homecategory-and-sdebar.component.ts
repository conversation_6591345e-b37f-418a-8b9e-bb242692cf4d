import { Component, Input, OnInit } from '@angular/core';
import { StoreFrontAPIConstant } from '../../constants/api.constants';
import { HomeService } from '../../services/home.service';
import { ComponentNameConstants } from '../../constants/components.contants';

@Component({
  selector: 'app-homecategory-and-sdebar',
  templateUrl: './homecategory-and-sdebar.component.html',
  styleUrls: ['./homecategory-and-sdebar.component.scss']
})
export class HomecategoryAndSdebarComponent implements OnInit {

  @Input() data: any;
  @Input() set allData(data: any) {
    if (data) {
      this.homeSidebar = this.service.getDataByComponentName(data, ComponentNameConstants.homeSidebar);
    }
  }
  
  homeSidebar: any = {};

  imgPath: string = StoreFrontAPIConstant.IMG_URL;

  constructor(private service: HomeService) { }

  ngOnInit(): void { }

}
