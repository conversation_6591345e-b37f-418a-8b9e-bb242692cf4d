<ng-container *ngIf="data?.store_manager_list.length">
    <section class="store-manager-sec">
        <div class="store-m-body">
            <h2>{{data?.store_manager_title}}</h2>
            <div class="store-m-list">
                <ul>
                    <ng-container *ngFor="let storemlist of data?.store_manager_list">
                        <li *ngIf="storemlist?.sidebar_list_link">
                            <a [routerLink]="[storemlist?.sidebar_list_link]">
                                <span class="material-symbols-outlined">{{storemlist?.sidebar_list_icon}}</span>
                                <span class="store-m-text">{{storemlist?.sidebar_list_name}}</span>
                            </a>
                        </li>
                    </ng-container>
                </ul>
            </div>
        </div>
    </section>
</ng-container>