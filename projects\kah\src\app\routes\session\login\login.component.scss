@import 'ngx-owl-carousel-o/lib/styles/scss/owl.carousel';
@import 'ngx-owl-carousel-o/lib/styles/scss/owl.theme.default';

::ng-deep {
  body {

    background-size: contain;
    height: 100vh;
    margin: 0;
  }
}

h1 {
  font-family: var(--brand-font-family-primary);
}

.sign_section {
  margin: 0;
  padding: 50px 0;
  height: 100vh;
  // overflow: auto;

  a.navbar-brand {
    margin: 0;
    padding: 0;
    position: relative;
    width: 150px;
    display: flex;

    img {
      margin: 0;
      padding: 0;
      width: 100%;
      height: 50px;
    }
  }

  .login_box {
    margin: 0 auto;
    max-width: 474px;


    h1 {
      margin: 0;
      font-weight: var(--brand-font-weight-bold);
      font-family: var(--brand-font-family-primary);
      color: var(--brand-color-primary);
      font-size: var(--brand-font-size-3);
      line-height: 42px;
    }

    span {
      margin: 8px 0 0 0;
      font-family: var(--brand-font-family-primary);
      color: var(--brand-grey);
      font-size: var(--brand-font-size-1);
      font-weight: var(--brand-font-weight-semi-bold);
    }


    .form-group {
      margin: 0 0 10px 0;
      padding: 0;
      position: relative;

      &::before {
        position: absolute;
        content: "";
        font-family: "Material icons Outlined";
        top: 0;
        left: 10px;
        bottom: 0;
        margin: auto 0;
        font-size: var(--brand-font-size-2);
        color: var(--brand-button-color-primary);
        font-weight: var(--brand-font-weight-normal);
        line-height: 23px;
        height: fit-content;
      }

      label {
        margin: 0 0 8px 0;
        padding: 0;
        display: flex;
        align-items: center;
        font-size: var(--brand-font-size-0-875);
        color: var(--brand-color-dark-600);
        font-weight: var(--brand-font-weight-semi-bold);
        line-height: 13px;
        text-transform: uppercase;
      }

      .form-control {
        margin: 0;
        padding: 0 15px 0 40px;
        height: 48px;
        font-size: var(--brand-font-size-0-875);
        font-weight: var(--brand-font-weight-semi-bold);
        background: var(--brand-color-white);
        border-radius: 8px;
        border: 1px solid var(--brand-accordian-button-color) !important;
      }

      span {
        margin: 15px 0 0 0;
        padding: 0;
        display: block;
        text-align: right;
        font-size: var(--brand-font-size-1);
        font-weight: var(--brand-font-weight-semi-bold);
        color: var(--brand-grey);
        font-family: var(--brand-font-family-primary);
      }
    }

    .user-name::before {
      content: '';
      position: absolute;
      background: url(../../../../assets/images/mail.svg);
      top: 19px;
      width: 20px;
      height: 18px;
      display: inline-flex;
    }

    .user-pass::before {
      // content: "\e897";
      content: '';
      position: absolute;
      background: url(../../../../assets/images/lock.svg);
      top: -20px;
      width: 20px;
      height: 18px;
      display: inline-flex;
      z-index: 1;
    }

    .visible-pass {
      position: absolute;
      right: 10px;
      top: 13px;
      cursor: pointer;
    }

    .form-group.form-check {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      gap: 0 8px;

      input {
        height: 16px;
        width: 16px;
        min-width: fit-content !important;
        min-height: fit-content !important;
        margin: 0;
      }

      label {
        margin: 0 !important;
        padding: 0;
      }
    }

    .button-section {
      display: flex;
      gap: 0 12px;
      cursor: pointer;

      button {
        margin: 0;
        padding: 0;
        border-radius: 10px;
        display: flex;
        font-size: var(--brand-font-size-1);
        height: 52px;
        justify-content: center;
        align-items: center;
        line-height: 14px;
        width: 100%;
        padding-bottom: 0;
        padding-top: 0;
        color: var(--brand-color-white);
        background: var(--brand-color-primary);
        position: relative;
        text-transform: uppercase;
        font-weight: var(--brand-font-weight-normal);
        transition: all 0.3s ease-in-out;
        border: none;
        font-family: var(--brand-font-family-primary);
      }

      button.btn.btn-primary {
        background: transparent !important;
        border: 1px solid var(--brand-color-dark-secondary) !important;
        color: var(--brand-color-dark-secondary) !important;
      }
    }
  }
}

.sign_section {
  .login_box .form-group.user-sso {
    margin: 0 0 30px 0 !important;
    display: flex;
    align-items: center;
    gap: 0 8px;
    padding: 4px !important;
    height: 52px;
    width: 100%;
    background: var(--brand-color-white);
    border: 1px solid var(--brand-accordian-button-color) !important;
    border-radius: 8px;

    &::before {
      display: none;
    }

    .form-control {
      height: 46px !important;
      width: 100%;
      color: var(--brand-color-dark-600);
      background: url(../../../../assets/images/key.svg) 10px center;
      background-repeat: no-repeat;
      background-size: 22px;
      appearance: auto;
      box-shadow: none;
      border: none !important;
    }

    a.sso-btn {
      margin: 0;
      padding: 0 10px;
      display: flex;
      align-items: center;
      justify-content: center;
      height: 100%;
      min-width: 132px;
      font-size: var(--brand-font-size-0-875);
      color: var(--brand-color-white);
      font-weight: var(--brand-font-weight-semi-bold);
      background: var(--brand-color-secondary);
      border-radius: 6px;
      text-transform: none !important;

      &:hover {
        background: var(--brand-color-primary);
      }
    }

  }
}

.img-container {
  max-width: 48%;
}

.home-banner-img {
  .owl-carousel {

    .owl-item {
      height: 100vh;
    }
  }
}

@media (max-width: 770px) {
  .img-container {
    max-width: 96%;
  }
}

.sso-btn {
  color: var(--brand-button-color-primary);
}

/*----HOME BANNER----*/
.home-banner-sec {
  margin: 0;
  padding: 50px 0;
  position: relative;
  display: flex;
  align-items: center;
  height: 100vh;

  .home-banner-img {
    margin: 0;
    padding: 0;
    position: fixed;
    left: 0;
    top: 0;
    width: 100%;
    z-index: 0;
    height: 100%;
    overflow: hidden;
    width: 50%
  }

  .home-banner-body {
    margin: 0 auto;
    padding: 0 30px;
    position: relative;
    width: 100%;
    max-width: 1500px;
    display: flex;
    flex-flow: column;
    gap: 32px 0;

    h1 {
      margin: 0;
      padding: 0;
      position: relative;
      display: flex;
      line-height: 32px;
      font-size: var(--brand-font-size-5);
      color: var(--brand-color-text-primary);
    }

    .h-banner-search-form,
    .h-banner-search-form form {
      position: relative;
    }
  }
}

.up-down-arrow {
  transition: transform 0.3s ease;

  img {
    max-width: 25px;
    transform: rotate(180deg);
  }
}

.rotate-arrow {
  transform: rotate(180deg);
}

.copy-right {
  font-size: var(--brand-font-size-0-875);
  color: var(--brand-color-dark-600);
  font-weight: var(--brand-font-weight-semi-bold);
}

.nav-link {
  li {
    a {
      font-size: var(--brand-font-size-0-875);
      color: var(--brand-color-dark-600);
      font-weight: var(--brand-font-weight-semi-bold);
      text-transform: capitalize;

      &:hover {
        text-decoration: underline !important;
      }
    }
  }
}

/*----HOME BANNER----*/

.custom-box {
  box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.0901960784);
  border: 1px solid var(--brand-accordian-button-color);
  padding: 1rem 1rem;
  display: flex;
  flex-direction: column;
  border-radius: 12px;
  max-width: 474px;
}

.form-field-full {
  background-color: var(--brand-border-color);
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  border-radius: 12px;
}

.login-header {
  cursor: pointer;
  min-height: 120px;

  h4 {
    font-family: var(--brand-font-family-primary);
    color: var(--brand-color-primary);
    font-weight: var(--brand-font-weight-bold);
  }

  p {
    font-family: var(--brand-font-family-primary);
    color: var(--brand-grey);
    font-size: var(--brand-font-size-0-875) !important;
    max-width: 90%;
    font-weight: var(--brand-font-weight-semi-bold);
  }
}


@media(max-width:990px) {
  .home-banner-sec {
    display: none;
  }
}

@media(max-width:767px) {
  .sign_section {
    .login_box {
      h1 {
        font-size: var(--brand-font-size-1-25);
        line-height: 1.2;
      }

      span {
        font-size: var(--brand-font-size-0-875);
      }
    }
  }

  .login-header {
    h4 {
      font-size: var(--brand-font-size-1-25) !important;
    }

    p {
      font-size: var(--brand-font-size-0-875) !important;
    }
  }
}

.custom-slide {
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f0f0f0;
  width: 100%;
}

::ng-deep .advt-banner-section {
  .owl-carousel {
    .owl-item {
      height: 100vh;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }
  }
}