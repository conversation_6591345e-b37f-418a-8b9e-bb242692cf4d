import { NgModule, APP_INITIALIZER } from "@angular/core";
import { BrowserAnimationsModule } from "@angular/platform-browser/animations";
import { HttpClientModule, HttpClient, HTTP_INTERCEPTORS, } from "@angular/common/http";
import { BrowserModule } from "@angular/platform-browser";
import { TranslateModule, TranslateLoader } from "@ngx-translate/core";
import { TranslateHttpLoader } from "@ngx-translate/http-loader";

import { AppComponent } from "./app.component";
import { AppRoutingModule } from "./app-routing.module";
import { SharedModule } from "./shared/shared.module";

import { appInitializerProviders } from "./core";
import { AuthInterceptor } from "./core/authentication/auth.intreceptor";

import {
  ApiConstantsService,
  AppFeatureService,
  AuthFeaturesGuard,
  AuthGuard,
  AuthSelectCustomerGuard,
  AuthService,
  CartService,
  ConfigService,
  LayoutModule,
  LayoutService,
  NgSnjyaModule,
  SelectCustomerModule,
  ToastModule,
  UserRoleService,
  UsersService,
} from "ng-snjya";
import { ENV_CONFIG, ROUTER_URL } from "./routes/ng-snjya.config.token";
import { environment } from "../environments/environment";
import { CommonModule } from "@angular/common";
import { NgSelectModule } from "@ng-select/ng-select";

export function TranslateHttpLoaderFactory(http: HttpClient) {
  return new TranslateHttpLoader(http, "./assets/i18n/", ".json");
}

function initializeApp(): Promise<any> {
  return new Promise((resolve, reject) => {
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.has("authtoken")) {
      const authtoken: any = urlParams.get("authtoken");
      localStorage.setItem("authtoken", authtoken);
    }
    resolve(true);
  });
}

const router_url: any = {
  AUTH: 'auth',
  STOREFRONT: 'store',
  BACKOFFICE: 'backoffice',
}

@NgModule({
  imports: [
    AppRoutingModule,
    BrowserModule,
    BrowserAnimationsModule,
    SharedModule,
    HttpClientModule,
    NgSnjyaModule.forRoot(environment),
    ToastModule,
    CommonModule,
    SelectCustomerModule,
    LayoutModule,
    TranslateModule.forRoot({
      loader: {
        provide: TranslateLoader,
        useFactory: TranslateHttpLoaderFactory,
        deps: [HttpClient],
      },
    }),
    NgSelectModule
  ],
  declarations: [AppComponent],
  providers: [
    {
      provide: APP_INITIALIZER,
      useFactory: () => initializeApp,
      multi: true,
    },
    appInitializerProviders,
    {
      provide: HTTP_INTERCEPTORS,
      useClass: AuthInterceptor,
      multi: true,
    },
    { provide: ENV_CONFIG, useValue: environment },
    { provide: ROUTER_URL, useValue: router_url },
    ApiConstantsService,
    AppFeatureService,
    AuthService,
    AuthSelectCustomerGuard,
    AuthFeaturesGuard,
    AuthGuard,
    CartService,
    UsersService,
    UserRoleService,
    LayoutService,
    ConfigService
  ],
  bootstrap: [AppComponent],
})
export class AppModule { }
