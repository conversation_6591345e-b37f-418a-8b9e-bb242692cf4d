:host {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: flex-end;
}

:host ::ng-deep {
    .ag-header {
        background-color: var(--brand-color-primary);
    }

    .ag-ltr .ag-cell-focus:not(.ag-cell-range-selected):focus-within,
    .ag-ltr .ag-context-menu-open .ag-cell-focus:not(.ag-cell-range-selected),
    .ag-ltr .ag-full-width-row.ag-row-focus:focus .ag-cell-wrapper.ag-row-group,
    .ag-ltr .ag-cell-range-single-cell,
    .ag-ltr .ag-cell-range-single-cell.ag-cell-range-handle,
    .ag-rtl .ag-cell-focus:not(.ag-cell-range-selected):focus-within,
    .ag-rtl .ag-context-menu-open .ag-cell-focus:not(.ag-cell-range-selected),
    .ag-rtl .ag-full-width-row.ag-row-focus:focus .ag-cell-wrapper.ag-row-group,
    .ag-rtl .ag-cell-range-single-cell,
    .ag-rtl .ag-cell-range-single-cell.ag-cell-range-handle {
        border: none;
    }

    .ag-theme-alpine {
        --ag-header-height: 30px;
        --ag-header-padding: 16px;
        --ag-header-foreground-color: var(--snjy-color-main-background);
        --ag-header-background-color: #0077d7;
        --ag-header-borders: 1px solid rgba(208, 215, 216, 0.3882352941);
        --ag-header-box-shadow: var(--snjy-box-shadow);
        --ag-header-borders-radius: 12px --ag-row-background-color: #0077d7;
    }

    .ag-row {
        --ag-row-background-color: 'grey';
    }

    .ag-root-wrapper {
        border: none;
    }

    .ag-header {
        border-radius: 10px;
        margin-bottom: 10px;
    }

    .ag-row {
        padding: 0 1rem;
        overflow: hidden;
        border: none;
        cursor: pointer;
    }

    .ag-row-selected::before,
    .ag-row-hover::before {
        background-color: transparent;
        background-image: none;
    }


    .ag-cell {
        height: calc(100% - 10px);
        display: flex;
        align-items: center;
        background: #f5f5f5;
        margin-bottom: 10px;

        &:first-child {
            border-top-left-radius: 10px;
            color: var(--brand-color-primary);
            border-bottom-left-radius: 10px;
        }

        &:last-child {
            border-top-right-radius: 10px;
            border-bottom-right-radius: 10px;
        }
    }

    .grid-cell {
        line-height: 1rem;
        font-weight: var(--snjy-font-weight-medium);

        .material-icons-outlined {
            color: #b7b7b7;
            font-size: var(--snjy-font-size-1-125);
        }
    }

    .ag-paging-panel {
        border: none;
        height: 50px;
    }
}

.export {
    font-size: var(--snjy-font-size-0-8);
    color: #4e4e4e;
    text-transform: uppercase;
    border: 1px solid #dfdfdf;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    background: var(--snjy-font-color-primary);
    font-weight: var(--snjy-font-weight-medium);
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 3px 3px rgba(42, 52, 59, 0.08);

    .material-icons-outlined {
        color: var(--snjy-button-color-primary);
    }
}