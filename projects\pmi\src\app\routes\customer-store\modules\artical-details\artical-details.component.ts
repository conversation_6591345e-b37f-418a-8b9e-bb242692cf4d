import { HttpClient } from '@angular/common/http';
import { Component, ElementRef, Renderer2 } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { StoreFrontAPIConstant } from '../../constants/api.constants';
import { DomSanitizer, SafeHtml } from '@angular/platform-browser';
import { AccountService } from '../../services/account.service';
import { ArticleService } from '../../services/article.service';
import { environment } from '../../../../../environments/environment';
@Component({
  selector: 'pmi-artical-details',
  templateUrl: './artical-details.component.html',
  styleUrls: ['./artical-details.component.scss']
})
export class ArticalDetailsComponent {
  id: string = '';
  slug: string = '';
  loading: boolean = false;
  articleDetails: any = {};
  articleBody!: SafeHtml;
  bannerImage = '';
  videoUrl: string = '';
  videoMimeType: string = '';
  attachments: any[] = [];
  views = 0;
  isFav = false;
  loadingFav: boolean = false;
  isPreview: boolean = false;
  constructor(
    private route: ActivatedRoute,
    private http: HttpClient,
    private accountService: AccountService,
    private articlesService: ArticleService,
    private sanitizer: DomSanitizer,
    private el: ElementRef, private renderer: Renderer2
  ) { }

  ngOnInit(): void {
    this.route.queryParams.subscribe((params: any) => {
      const isPreview = params.preview;
      this.isPreview = !!isPreview || false;
    });
    this.route.paramMap.subscribe(params => {
      const id = params.get('id');
      this.slug = id || '';
      setTimeout(()=>{
        this.fetchArticleDetails();
      }, 300);
    });
  }

  fetchFav() {
    this.loadingFav = true;
    this.accountService.getFavoritesById(this.id).subscribe((res) => {
      if (res?.data?.id) {
        this.isFav = true;
      }
      this.loadingFav = false;
    }, () => {
      this.loadingFav = false;
    })
  }

  toggleFav() {
    if (this.isFav) {
      this.accountService.makeFavorite({
        "article_id": this.id,
        "description": this.articleDetails.Heading,
        "slug": this.slug
      }).subscribe();
    } else {
      this.accountService.deleteFavorite(this.id).subscribe();
    }
  }

  fetchArticleDetails(): void {
    this.loading = true;
    let url = `${StoreFrontAPIConstant.Articles}?populate=deep,2&filters[slug][$eq]=${this.slug}`;
    if(this.isPreview) {
      url += `&publicationState=preview`;
    }
    this.http.get(url).subscribe({
      next: (value: any) => {
        this.loading = false;
        if (value?.data[0]?.id && value?.data[0]?.attributes) {
          this.articleDetails = { ...value?.data[0].attributes, id: value?.data[0].id };
          this.id = this.articleDetails.id || '';
          this.articleBody = this.sanitizer.bypassSecurityTrustHtml(this.articleDetails.Body);
          this.views = parseInt(this.articleDetails.Views) + 1;
          if(!this.isPreview) {
            this.fetchFav();
            this.updateViewCount();
          }
          if (this.articleDetails?.Banner_Image?.data?.attributes?.url) {
            this.bannerImage = this.articleDetails.Banner_Image.data.attributes.url;
          }
          if (this.articleDetails?.Videos?.data?.length && this.articleDetails?.Videos?.data[0]?.attributes?.url) {
            this.videoUrl = this.articleDetails.Videos.data[0].attributes.url;
            this.videoMimeType = this.articleDetails.Videos.data[0].attributes.mime;
          }
          if (this.articleDetails?.Attachments?.data?.length) {
            this.attachments = this.articleDetails.Attachments.data;
          }
          setTimeout(() => {
            this.addClassToElements();
          }, 100);
        }
      },
    });
  }

  addClassToElements() {
    const elements = this.el.nativeElement.querySelectorAll('table');
    elements.forEach((element: HTMLElement) => {
      this.renderer.addClass(element, 'table');
      this.renderer.addClass(element, 'table-bordered');
    });
    const figs = this.el.nativeElement.querySelectorAll('figure');
    figs.forEach((element: HTMLElement) => {
      this.renderer.removeClass(element, 'table');
    });
    const images = this.el.nativeElement.querySelectorAll('.article-details-html img');
    if (images.length) {
      images.forEach(async (element: HTMLElement) => {
        const src = element?.attributes?.getNamedItem("src")?.value || '';
        const objcetName = this.getObjectName(src);
        if (objcetName) {
          const url = await this.articlesService.getSignedUrl(objcetName);
          this.renderer.setAttribute(element, 'src', url);
          this.renderer.removeAttribute(element, 'srcset');
        }
      });
    }
    const videos = this.el.nativeElement.querySelectorAll('.article-details-html video source');
    if (videos.length) {
      videos.forEach(async (element: HTMLElement) => {
        const src = element?.attributes?.getNamedItem("src")?.value || '';
        const objcetName = this.getObjectName(src);
        if (objcetName) {
          const url = await this.articlesService.getSignedUrl(objcetName);
          this.renderer.setAttribute(element, 'src', url);
        }
      });
    }
    const a = this.el.nativeElement.querySelectorAll('.article-details-html a');
    if (a.length) {
      a.forEach(async (element: HTMLElement) => {
        const src = element?.attributes?.getNamedItem("href")?.value || '';
        if (src.startsWith(`https://${environment.bucket}`)) {
          const objcetName = this.getObjectName(src);
          if (objcetName) {
            const url = await this.articlesService.getSignedUrl(objcetName);
            this.renderer.setAttribute(element, 'href', url);
          }
        }
      });
    }
  }

  getObjectName(url: string): string {
    const baseUrl = url.split('?')[0];
    const segments = baseUrl.split('/');
    return segments[segments.length - 1];
  }

  updateViewCount() {
    const url = `${StoreFrontAPIConstant.Articles}/${this.id}`;
    this.http.put(url, {
      "data": {
        "Views": this.views,
      },
    }).subscribe();
  }

  download(url: string, name: string) {
    let link = document.createElement("a");
    link.href = url;
    link.download = name;
    link.target = "_blank";
    link.click();
  }

  getFileIconClass(ext: string) {
    ext = ext.substring(1);
    let iconClass = 'fas fa-file';
    const fileIcons: any = {
      'pdf': 'picture_as_pdf',
      'doc': 'description',
      'docx': 'description',
      'xls': 'description',
      'xlsx': 'description',
      'ppt': 'description',
      'pptx': 'description',
      'jpg': 'image',
      'jpeg': 'image',
      'png': 'image',
      'gif': 'image',
      'mp3': 'audiotrack',
      'wav': 'audiotrack',
      'mp4': 'videocam',
      'avi': 'videocam',
      'txt': 'description',
      'zip': 'archive',
      'rar': 'archive',
      'js': 'code',
      'html': 'code',
      'css': 'code',
    };

    if (fileIcons.hasOwnProperty(ext)) {
      iconClass = fileIcons[ext];
    }

    return iconClass;
  }


}
