{"admin": {"menu": {"home": "Home", "dashboard": "Dashboard", "product": "Product", "customer": "Customer", "contacts": "Contacts", "logout": "Logout", "store": "store", "settings": "Settings", "configs": "Configuration", "admin": "Administration", "products": "Products"}}, "validation": {"required": "This field is required", "email": {"pattern": "Please enter a valid email"}, "phone": {"pattern": "Please enter a valid phone number"}, "passwordNotMatched": "Password not matching.", "invalidRange": "Valid to should not be less than <PERSON><PERSON> from.", "size_96": "Please upload image of dimenssion less than or equal to 96X96", "size_300": "Please upload image of dimenssion less than or equal to 300X300", "size_1200": "Please upload image of dimenssion less than or equal to 1200X1200"}, "form": {"details": "Details", "action": {"submit": "Submit"}, "submit": {"success": "Changes saved successfully!"}, "imageRemoved": "Image deleted successfully"}, "backoffice": {"partner": {"id": "ID", "name": "Name", "type": "Type", "category": "Category", "partnerID": "Partner Id", "fname": "First Name", "lname": "Last Name", "email": "Email", "phone": "Phone", "company": "Company", "address": "Address", "partnerCategory": "Partner Category", "partnerFullName": "Partner Full Name", "partnerGrouping": "Partner Grouping", "partnerType": "Partner Type", "partnerUUID": "Partner UUID", "partnerName": "Partner Name", "companyCode": "Company Code", "customerCode": "Customer Code", "deletionIndicator": "Deletion Indicator", "customerAccountGroup": "Customer Account Group", "salesOrg": "Sales Org", "distributionChannel": "Distribution Channel", "division": "Division", "partnerCounter": "Partner Counter", "partnerFunction": "Partner Function", "customerNumber": "Customer No", "salesArea": "Sales Area", "organization": "Organization", "currency": "<PERSON><PERSON><PERSON><PERSON>", "customerAccountAssignmentGroup": "Account Assignment Group", "customerGroup": "Customer Group", "customerPriceGroup": "Customer Price Group", "customerPricingProcedure": "Customer Pricing Procedure", "deliveryBlockedForCustomer": "Delivery Blocked For Customer", "salesGroup": "Sales Group", "salesOffice": "Sales Office", "shippingCondition": "Shipping Condition", "supplyingPlant": "Supplying Plant", "accountGroup": "Account Group"}, "product": {"desc": "Pdt. description", "productId": "Product ID", "category": "Category", "price": "Price", "price_range": "Price Range", "description": "Product Description", "type": "Product Type", "typeDescription": "Product Type Description", "oldID": "Product Old ID", "oldIDDescription": "Product Old ID Description", "group": "Product Group", "groupDescription": "Product Group Description", "divisionID": "Division ID", "divisionDescription": "Division Description", "itemCategoryGroup": "Item Category Group", "itemCategoryGroupDescription": "Item Category Group Description", "grossWeight": "Gross Weight", "grossWeightDescription": "Gross Weight Description", "weightUnit": "Weight Unit", "weightUnitDescription": "Weight Unit Description", "weightISOUnit": "Weight ISO Unit", "weightISOUnitDescription": "Weight ISO Unit Description", "baseUnit": "Base Unit", "baseUnitDescription": "Base Unit Description", "baseISOUnit": "Base ISO Unit", "baseISOUnitDescription": "Base ISO Unit Description", "netWeight": "Net Weight", "netWeightDescription": "Net Weight Description", "volumeUnit": "Volume Unit", "volumeUnitDescription": "Volume Unit Description", "volumeISOUnit": "Volume ISO Unit", "volumeISOUnitDescription": "Volume ISO Unit Description", "volume": "Product Volume", "volumeDescription": "Product Volume Description", "thumbnails": "Thumbnails", "catalog": "Catalog", "salesOrgId": "Sales Org Id", "salesOrgDesc": "Sales Org Description", "distriChannelId": "Distribution Channel ID", "distriChannelDesc": " Distribution Channel Description", "plantId": "Plant Id", "plantDesc": "Plant Description", "isDeleted": "Mark For Deletion", "unit_measure": "UoM", "images": "Images", "summary": "Summary", "specification": "Specification", "status": "Status", "name": "Name", "code": "Code", "stock": "Stock", "currency_iso": "Currency ISO", "formatted_value": "Formatted Value", "max_quantity": "<PERSON>", "min_quantity": "<PERSON>", "price_type": "Price Type", "value": "Value", "max_price": "Max Price", "min_price": "<PERSON>", "category_code": "Category Code", "catalog_code": "Catalog Code", "category_name": "Category Name", "classification_code": "Classification Code", "classification_features": "Classification Features", "classification_name": "Classification Name", "feature_code": "Feature Code", "feature_desc": "Feature Desc", "feature_unit": "Feature Unit", "feature_values": "Feature Values", "feature_name": "Feature Name", "feature_range": "Feature Range", "feature_type": "Feature Type", "feature_unit_name": "Feature Unit Name", "symbol": "Symbol", "photo_sm": "Image SM(96x96)", "photo_md": "Image MD(300x300)", "photo_lg": "Image LG(1200x1200)", "photo_sm_url": "Image SM(96x96) Url", "photo_md_url": "Image MD(300x300) Url", "photo_lg_url": "Image LG(1200x1200) Url", "url": "Url", "selectDimenssion": "Select dimenssion", "documentName": "Document Name", "docType": "Doc Type", "plant": "Plant"}, "contact": {"firstName": "First Name", "lastName": "Last Name", "email": "Email", "phone": "Phone", "address": "Address", "validFrom": "Valid Form", "validTo": "<PERSON><PERSON>", "status": "Status", "customerID": "Customer ID", "customerName": "Customer Name", "accountGUID": "GUID", "password": "New Password", "confirmPassword": "Confirm New Password"}}}