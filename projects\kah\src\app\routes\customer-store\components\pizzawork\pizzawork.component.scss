/*----SUBMIT A PIZZA SEC----*/
.submit-a-pizza-sec {
    margin: 0;
    padding: 60px 0;
    position: relative;
    background: url(/assets/images/background-img.jpg) center -201px no-repeat;
    background-size: cover;
    height: auto;

    .submit-a-pizza-body {
        margin: 0 auto;
        padding: 0 30px;
        max-width: 1500px;
        width: 100%;
        position: relative;
        gap: 80px 0;

        .submit-a-pizza-work {
            position: relative;
            text-align: center;
            display: flex;
            flex-direction: column;
            gap: 30px 0;

            .submit-a-pizza-logo {
                max-width: 220px;
                margin: 0 auto;
                position: relative;

                img {
                    width: 100%;
                }
            }

            .submit-a-pizza-title {
                margin: 0;
                padding: 0;
                position: relative;
                line-height: 32px;
                font-size: var(--brand-font-size-5);
                color: var(--brand-color-tertiary-dark-teal);
                font-weight: var(--brand-font-weight-medium);
            }

            .submit-a-pizza-desc {
                margin: 0;
                padding: 0;
                position: relative;
                line-height: 24px;
                font-size: 22px;
                color: var(--brand-color-black);
                font-weight: var(--brand-font-weight-light);
            }

            .submit-a-pizza-btn {
                margin: 30px 0 0 0;
                gap: 0 30px;

                a {
                    margin: 0;
                    padding: 0;
                    position: relative;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    width: 380px;
                    height: 50px;
                    font-size: var(--brand-font-size-0-9);
                    font-weight: var(--brand-font-weight-semi-bold);
                    color: var(--brand-color-primary);
                    gap: 0 5px;
                    text-transform: uppercase;
                    background: #FFF;
                    border-radius: 8px;
                    letter-spacing: 2px;
                }
            }
        }


        .signage-and-list {
            position: relative;
            margin: 0;
            gap: 0 5%;

            .signage-sec,
            .list-sec {
                margin: 0;
                padding: 30px;
                position: relative;
                flex: 1;
                height: 450px;
                background: #FFF;
                border-radius: 20px;
            }

            .signage-sec {
                background: url(/assets/images/signage-box-bg.jpg) center center no-repeat;
                background-size: cover;

                .signage-left {
                    position: relative;
                    flex: 1;

                    .signage-title {
                        margin: 40px 0 20px 0;
                        padding: 0;
                        position: relative;
                        line-height: 42px;
                        font-size: 36px;
                        color: var(--brand-color-secondary-neutral-charcoal);
                        font-weight: var(--brand-font-weight-bold);
                    }

                    .signage-p {
                        margin: 0 0 50px 0;
                        padding: 0;
                        position: relative;
                        font-family: var(--brand-font-family-secondary);
                        font-size: var(--brand-font-size-1-125);
                        color: var(--brand-color-secondary-neutral-charcoal);
                        line-height: 24px;
                    }

                    .signage-btn {
                        a {
                            margin: 0;
                            padding: 0;
                            position: relative;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            width: 220px;
                            height: 50px;
                            font-size: var(--brand-font-size-0-9);
                            font-weight: var(--brand-font-weight-normal);
                            color: var(--bs-white);
                            gap: 0 5px;
                            text-transform: uppercase;
                            background: var(--brand-color-tertiary-dark-teal);
                            border-radius: 8px;
                            letter-spacing: 1px;
                        }
                    }
                }

                .signage-right {
                    position: relative;
                    flex: 1;

                    img {
                        width: 100%;
                    }
                }
            }

            .list-sec {
                position: relative;
                gap: 0 4%;

                .list-box {
                    position: relative;
                    flex: 1;

                    .list-title {
                        margin: 0 0 22px 0;
                        padding: 0;
                        position: relative;
                        line-height: 32px;
                        font-size: var(--brand-font-size-22);
                        color: var(--brand-color-tertiary-dark-teal);
                        font-weight: var(--brand-font-weight-medium);
                    }

                    ul {
                        position: relative;
                        gap: 15px 0;

                        a {
                            position: relative;
                            display: flex;
                            gap: 0 8px;
                            color: #000;

                            .material-symbols-outlined {
                                font-weight: var(--brand-font-weight-bolder);
                                color: var(--brand-color-primary);
                            }

                            .list-text {
                                margin: 0;
                                padding: 0;
                                position: relative;
                                font-family: var(--brand-font-family-secondary);
                                font-size: var(--brand-font-size-1);
                                color: var(--brand-color-secondary-neutral-charcoal);
                                line-height: 22px;

                                .date {
                                    display: flex;
                                    margin: 0;
                                    padding: 0;
                                    position: relative;
                                    line-height: 18px;
                                    font-size: var(--brand-font-size-0-875);
                                    color: var(--brand-color-secondary-neutral-charcoal);
                                    font-weight: var(--brand-font-weight-bold);
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}

/*----SUBMIT A PIZZA SEC----*/



.webminar-sec {
    margin: 0;
    padding: 0;
    position: relative;

    .webminar-body {
        margin: 0;
        padding: 0;
        position: relative;

        .webminar-img {
            margin: 0;
            padding: 0;
            position: relative;
            height: 530px;
            width: 100%;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;

            &:before {
                position: absolute;
                content: '';
                left: 0;
                top: 0;
                width: 100%;
                height: 100%;
                background: linear-gradient(45deg, #202124 2%, transparent 100%);
            }
        }

        .webminar-content {
            margin: auto;
            padding: 50px 30px;
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            max-width: 1500px;
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: flex-end;

            .webminar-date {
                margin: 0;
                padding: 0;
                position: relative;
                width: 138px;
                height: 35px;
                display: flex;
                align-items: center;
                gap: 0 8px;
                font-size: 14px;
                font-weight: 600;
                color: #05a4b0;
                border-radius: 8px;
            }

            .webminar-title {
                margin: 20px 0 30px 0;
                padding: 0 0 20px 0;
                position: relative;
                line-height: 36px;
                font-size: var(--brand-font-size-4);
                color: var(--brand-color-white);
                font-weight: var(--brand-font-weight-medium);

                &:before {
                    position: absolute;
                    content: '';
                    left: 2px;
                    bottom: 0;
                    width: 196px;
                    height: 4px;
                    background: linear-gradient(90deg, #fff, transparent);
                    border-radius: 50px;
                }
            }

            .webminar-desc {
                margin: 0 0 50px 0;
                padding: 0;
                position: relative;
                display: flex;
                font-size: var(--brand-font-size-1);
                font-weight: var(--brand-font-weight-light);
                color: var(--brand-border-color);
                max-width: 600px;
            }

            a {
                margin: 0;
                padding: 0;
                position: relative;
                display: flex;
                align-items: center;
                justify-content: center;
                width: 190px;
                height: 50px;
                font-size: var(--brand-font-size-0-9);
                font-weight: var(--brand-font-weight-semi-bold);
                color: var(--brand-color-white);
                gap: 0 5px;
                text-transform: uppercase;
                background: var(--brand-color-primary);
                border-radius: 8px;
            }
        }
    }
}