import { NgModule } from "@angular/core";
import { RouterModule, Routes } from "@angular/router";
// import { DashboardGuard } from "./guard/dashboard.guard";

const routes: Routes = [
  { path: "home", loadChildren: () => import("./modules/home/<USER>").then((mod) => mod.HomeModule), },
  { path: "article", loadChildren: () => import("./modules/artical-details/artical-details.module").then((mod) => mod.ArticalDetailsModule), },
  { path: "my-account", loadChildren: () => import("./modules/my-account/my-account.module").then((mod) => mod.MyAccountModule), },
  { path: "categories", loadChildren: () => import("./modules/categories/categories.module").then((mod) => mod.CategoriesModule), },
  { path: "", redirectTo: "home", pathMatch: "full" },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class CustomerStoreRoutingModule { }
