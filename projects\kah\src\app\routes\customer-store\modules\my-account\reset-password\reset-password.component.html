<div class="modal-header">
    <h4 class="modal-title" id="modal-basic-title">Reset Password: {{data?.contact?.contact_id}}</h4>
    <button type="button" class="btn-close" aria-label="Close" (click)="activeModal.dismiss()"></button>
</div>
<div class="modal-body">
    <form [formGroup]="form">
        <div class="form-group">
            <label>Current Password</label>
            <input type="password" formControlName="currentPassword" class="form-control mt-1 mb-2"
                autocomplete="new-password"
                [ngClass]="{ 'is-invalid': isPasswordFormSubmitted && f['currentPassword'].errors }" />
            <div *ngIf="isPasswordFormSubmitted && f['currentPassword'].errors" class="invalid-feedback">
                <div *ngIf="f['currentPassword'].errors['required']">
                    This field is required</div>
                <div *ngIf="f['currentPassword'].errors['minlength']">
                    Must be at least 8 characters</div>
                <div *ngIf="f['currentPassword'].errors['hasNumber']">
                    Must contain at least one number</div>
                <div *ngIf="f['currentPassword'].errors['hasCapitalCase']">
                    Must contain at least one Letter in Capital Case</div>
                <div *ngIf="f['currentPassword'].errors['hasSmallCase']">
                    Must contain at least one Letter in Small Case</div>
                <div *ngIf="f['currentPassword'].errors['hasSpecialCharacters']">
                    Must contain at least one Special Character</div>
            </div>
        </div>
        <div class="form-group mt-2">
            <label>Re-enter Password</label>
            <input type="password" formControlName="confirmPassword" class="form-control mt-1 mb-2"
                [ngClass]="{ 'is-invalid': isPasswordFormSubmitted && f['confirmPassword'].errors }" />
            <div *ngIf="isPasswordFormSubmitted && f['confirmPassword'].errors" class="invalid-feedback">
                <div *ngIf="f['confirmPassword'].errors['required']">
                    This field is required</div>
                <div *ngIf="f['confirmPassword'].errors['confirmedValidator']">
                    Passwords must match
                </div>
            </div>
        </div>
    </form>
</div>
<div class="modal-footer">
    <button class="btn btn-light me-2" (click)="activeModal.dismiss()">Cancel</button>
    <button (click)="onSubmit()" [disabled]="saving" class="btn btn-primary">{{ saving ? 'Changing Password' :
        'Change Password' }}</button>
</div>