<div class="flex-grow-1 d-flex flex-column justify-content-between h-100 p-3">
    <a class="navbar-brand" href="#"><img src="assets/images/kahala.svg" class="img-fluid"></a>
    <div class="forgot-password-box" *ngIf="!emailSent">
        <h4 class="mb-4">Forgot Password</h4>
        <div class="p-3 form-container">
            <form [formGroup]="form" class="p-4">
                <div class="form-group mb-4 required">
                    <label class="mb-2">Email</label>
                    <input type="text" formControlName="email" class="form-control mt-1 mb-2 email-icon"
                        placeholder="Enter your registerd email"
                        [ngClass]="{ 'is-invalid': submitted && f['email'].errors }" />
                    <div *ngIf="submitted && f['email'].errors" class="invalid-feedback">
                        <div *ngIf="f['email'].errors['required']">Email is required</div>
                        <div *ngIf="f['email'].errors['email']">Email is invalid</div>
                    </div>
                    <span class="form-text hint">We will send reset instructions on your registered email</span>
                </div>
                <div class="button-section mt-4 flex-column">
                    <button type="submit" class="btn btn-light w-100 btn-login" [disabled]="!!form.invalid || saving"
                        (click)="onSubmit()">Reset
                        Password</button>
                    <span class="form-text hint decoration-none text-center" role="button" [routerLink]="'../login'">
                        Back to Login</span>
                </div>
            </form>
        </div>
    </div>
    <div class="forgot-password-box text-center" *ngIf="emailSent">
        <span class="material-icons-outlined email">email</span>
        <h4 class="mb-4">Check You Email</h4>
        <span class="form-text hint">Please check the email address {{ email }} for instruction to reset your
            password.</span>
        <div class="button-section mt-4 flex-column">
            <button type="submit" class="btn btn-light w-100 btn-login" [disabled]="!!form.invalid || saving"
                (click)="onSubmit()">Resend Email</button>
            <span class="form-text hint decoration-none text-center" role="button" [routerLink]="'../login'">
                Back to Login</span>
        </div>
    </div>
    <div class="col-sm-12 d-flex flex-column flex-xl-row border-top align-items-center justify-content-between pt-3">
        <div class="copy-right fs-7">
            {{footerData?.footer_copyright}}
        </div>
        <ul class="nav-link mb-0 d-flex flex-row gap-3 fs-7">
            <ng-container *ngFor="let bottomFooterLinks of footerData?.Bottom_Footer_Links">
                <li *ngIf="bottomFooterLinks?.footer_link">
                    <a [href]="[bottomFooterLinks?.footer_link]" target="_blank">{{bottomFooterLinks?.footer_link_name}}</a>
                </li>
            </ng-container>
        </ul>
    </div>
</div>