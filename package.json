{"name": "mty-snjya", "version": "0.0.0", "scripts": {"start-pmi": "ng serve pmi", "build-pmi": "ng build pmi", "start-kah": "ng serve kah", "build-kah": "ng build kah", "build-all": "npm run build-pmi && npm run build-kah"}, "private": true, "dependencies": {"@angular/animations": "^16.1.0", "@angular/cdk": "^16.1.0", "@angular/common": "^16.1.0", "@angular/compiler": "^16.1.0", "@angular/core": "^16.1.0", "@angular/forms": "^16.1.0", "@angular/localize": "^16.1.3", "@angular/platform-browser": "^16.1.0", "@angular/platform-browser-dynamic": "^16.1.0", "@angular/router": "^16.1.0", "@kolkov/angular-editor": "^3.0.0-beta.0", "@ng-bootstrap/ng-bootstrap": "^15.0.0", "@ng-select/ng-select": "^11.2.0", "@ngx-translate/core": "^14.0.0", "@ngx-translate/http-loader": "^7.0.0", "@popperjs/core": "^2.11.8", "ag-grid-angular": "^31.0.1", "ag-grid-community": "^31.0.1", "bootstrap": "^5.2.3", "crypto-js": "4.1.1", "jquery": "^3.7.1", "moment": "^2.29.4", "ng-snjya": "file:ng-snjya-0.0.53.tgz", "ngx-image-zoom": "^2.1.0", "ngx-owl-carousel-o": "^16.0.0", "rxjs": "~7.8.0", "tslib": "^2.3.0", "xlsx": "0.14.0", "zone.js": "~0.13.0"}, "devDependencies": {"@angular-devkit/build-angular": "^16.1.8", "@angular/cli": "~16.1.0", "@angular/compiler-cli": "^16.1.0", "@types/crypto-js": "4.1.1", "@types/jasmine": "~4.3.0", "jasmine-core": "~4.6.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "typescript": "~5.1.3"}}