import { Component } from '@angular/core';
import { FormGroup, FormBuilder, Validators, AbstractControl, ValidationErrors, ValidatorFn } from '@angular/forms';
import { AuthService, ToastService, UsersService } from 'ng-snjya';
import { environment } from '../../../../../environments/environment';
import { ActivatedRoute } from '@angular/router';
import { catchError, concat, distinctUntilChanged, map, Observable, of, Subject, switchMap, takeUntil, tap } from 'rxjs';
import { RolesType } from 'projects/pmi/src/app/core/constants/constants';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { RegisterUserComponent } from './reset-password/reset-password.component';


function ConfirmedValidator(controlName: string, matchingControlName: string) {
  return (formGroup: FormGroup) => {
    const control = formGroup.controls[controlName];
    const matchingControl = formGroup.controls[matchingControlName];
    if (
      matchingControl.errors &&
      !matchingControl.errors['confirmedValidator']
    ) {
      return;
    }
    if (control.value !== matchingControl.value) {
      matchingControl.setErrors({ confirmedValidator: true });
    } else {
      matchingControl.setErrors(null);
    }
  };
}

function patternValidator(regex: RegExp, error: ValidationErrors): ValidatorFn {
  return (control: AbstractControl) => {
    if (!control.value) {
      // if control is empty return no error
      return null;
    }

    // test the value of the control against the regexp supplied
    const valid = regex.test(control.value);

    // if true, return no error (no error), else return error passed in the second parameter
    return valid ? null : error;
  };
}

@Component({
  selector: 'kah-my-account',
  templateUrl: './my-account.component.html',
  styleUrls: ['./my-account.component.scss']
})
export class MyAccountComponent {
  profileForm!: FormGroup;
  changePasswordForm!: FormGroup;
  registerForm!: FormGroup;
  isRegisterFormSubmitted = false;
  isFormSubmitted = false;
  isPasswordFormSubmitted = false;
  selectedSection = 'my-profile';
  favoriteArticles: any[] = [];
  loadingFavs = false;
  loadingUserDetails = false;
  userDetails: any = {};
  saving = false;
  registering = false;
  adminPortalUrl: string = environment.cmsEndpoint + '/admin';
  public functions = [
    { code: "0001", text: "Franchisee" },
    { code: "0002", text: "Landlord" },
    { code: "0003", text: "Leasing Contact" },
    { code: "0004", text: "Property Management" },
    { code: "0005", text: "Construction Contact" },
    { code: "Z001", text: "Partner" },
    { code: "Z002", text: "Broker" },
    { code: "Z003", text: "Billing Contact" },
    { code: "Z005", text: "IT Contact" },
  ];
  public functionsObj: any = {
    "0001": "Franchisee",
    "0002": "Landlord",
    "0003": "Leasing Contact",
    "0004": "Property Management",
    "0005": "Construction Contact",
    "Z001": "Partner",
    "Z002": "Broker",
    "Z003": "Billing Contact",
    "Z005": "IT Contact",
  }
  private ngUnsubscribe = new Subject<void>();

  private defaultOptions: any = [];
  public customers$!: Observable<any[]>;
  public customerLoading = false;
  public customerInput$ = new Subject<string>();
  public userRoleType = RolesType.STOREFRONT;
  public bpcustomers: any[] = [];
  public loadingbpusers = false;
  private bpUserDetails: any = {};
  public usStates = [
    { "key": "AL", "value": "Alabama" },
    { "key": "AK", "value": "Alaska" },
    { "key": "AZ", "value": "Arizona" },
    { "key": "AR", "value": "Arkansas" },
    { "key": "CA", "value": "California" },
    { "key": "CO", "value": "Colorado" },
    { "key": "CT", "value": "Connecticut" },
    { "key": "DE", "value": "Delaware" },
    { "key": "FL", "value": "Florida" },
    { "key": "GA", "value": "Georgia" },
    { "key": "HI", "value": "Hawaii" },
    { "key": "ID", "value": "Idaho" },
    { "key": "IL", "value": "Illinois" },
    { "key": "IN", "value": "Indiana" },
    { "key": "IA", "value": "Iowa" },
    { "key": "KS", "value": "Kansas" },
    { "key": "KY", "value": "Kentucky" },
    { "key": "LA", "value": "Louisiana" },
    { "key": "ME", "value": "Maine" },
    { "key": "MD", "value": "Maryland" },
    { "key": "MA", "value": "Massachusetts" },
    { "key": "MI", "value": "Michigan" },
    { "key": "MN", "value": "Minnesota" },
    { "key": "MS", "value": "Mississippi" },
    { "key": "MO", "value": "Missouri" },
    { "key": "MT", "value": "Montana" },
    { "key": "NE", "value": "Nebraska" },
    { "key": "NV", "value": "Nevada" },
    { "key": "NH", "value": "New Hampshire" },
    { "key": "NJ", "value": "New Jersey" },
    { "key": "NM", "value": "New Mexico" },
    { "key": "NY", "value": "New York" },
    { "key": "NC", "value": "North Carolina" },
    { "key": "ND", "value": "North Dakota" },
    { "key": "OH", "value": "Ohio" },
    { "key": "OK", "value": "Oklahoma" },
    { "key": "OR", "value": "Oregon" },
    { "key": "PA", "value": "Pennsylvania" },
    { "key": "RI", "value": "Rhode Island" },
    { "key": "SC", "value": "South Carolina" },
    { "key": "SD", "value": "South Dakota" },
    { "key": "TN", "value": "Tennessee" },
    { "key": "TX", "value": "Texas" },
    { "key": "UT", "value": "Utah" },
    { "key": "VT", "value": "Vermont" },
    { "key": "VA", "value": "Virginia" },
    { "key": "WA", "value": "Washington" },
    { "key": "WV", "value": "West Virginia" },
    { "key": "WI", "value": "Wisconsin" },
    { "key": "WY", "value": "Wyoming" }
  ]
  public canadaProvincesAndTerritories = [
    { "key": "AB", "value": "Alberta" },
    { "key": "BC", "value": "British Columbia" },
    { "key": "MB", "value": "Manitoba" },
    { "key": "NB", "value": "New Brunswick" },
    { "key": "NL", "value": "Newfoundland and Labrador" },
    { "key": "NS", "value": "Nova Scotia" },
    { "key": "ON", "value": "Ontario" },
    { "key": "PE", "value": "Prince Edward Island" },
    { "key": "QC", "value": "Quebec" },
    { "key": "SK", "value": "Saskatchewan" },
    { "key": "NT", "value": "Northwest Territories" },
    { "key": "NU", "value": "Nunavut" },
    { "key": "YT", "value": "Yukon" }
  ]

  constructor(
    private _snackBar: ToastService,
    private dialog: NgbModal,
    private fb: FormBuilder,
    private authService: AuthService,
    private manageUserService: UsersService,
    private route: ActivatedRoute
  ) { }

  ngOnInit(): void {
    this.setPermissions();
    this.bpUserDetails = this.authService.userDetail;
    this.userRoleType = this.authService.userDetail.role;
    this.getCustomers();
    this.route.queryParams.subscribe((params: any) => {
      if (params.t && ['my-profile', 'register-user', 'account-overview'].indexOf(params.t) != -1) {
        this.selectedSection = params.t;
      }
    });
    this.profileForm = this.fb.group({
      firstName: ['', Validators.required],
      lastName: ['', Validators.required],
      userName: [''],
      address: [''],
      email: ['', [Validators.required, Validators.email]],
      city: [''],
      country: [''],
      zipCode: [''],
      state: [''],
      directPhone: ['', Validators.pattern("^\\+[0-9]{1,3}[0-9]{5,14}$")],
      officePhone: ['', Validators.pattern("^\\+[0-9]{1,3}[0-9]{5,14}$")],
      cellPhone: ['', Validators.pattern("^\\+[0-9]{1,3}[0-9]{5,14}$")],
      otherPhone: ['', Validators.pattern("^\\+[0-9]{1,3}[0-9]{5,14}$")],
      fax: [''],
      dateOfBirth: [''],
      gender: [''],
    });
    this.changePasswordForm = this.fb.group({
      currentPassword: [
        '',
        [
          Validators.required,
          Validators.minLength(8),
          // check whether the entered password has a number
          patternValidator(/\d/, {
            hasNumber: true,
          }),
          // check whether the entered password has upper case letter
          patternValidator(/[A-Z]/, {
            hasCapitalCase: true,
          }),
          // check whether the entered password has a lower case letter
          patternValidator(/[a-z]/, {
            hasSmallCase: true,
          }),
          // check whether the entered password has a special character
          patternValidator(/[ !@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/, {
            hasSpecialCharacters: true,
          }),
        ],
      ],
      confirmPassword: [''],
    }, {
      validators: ConfirmedValidator('currentPassword', 'confirmPassword')
    });
    this.fetchUserDetails();
    this.setRegisterForm();
    this.loadCustomers();
  }

  getCmsRoles(): any[] {
    const cmsInfo = localStorage.getItem('userInfo');
    if (cmsInfo) {
      try {
        const info = JSON.parse(cmsInfo);
        if (info?.userRoles?.length) {
          return info.userRoles.map((role: any) => role.name);
        }
      } catch (error) {
        console.log(error);
      }
    }
    return [];
  }

  show = {
    adaptUi: false,
    register: false,
    backOffice: false,
    overview: false
  };
  profileEditable = true;

  setPermissions() {
    const accessibleUserRoles = ["ACCOUNT"];
    const cmsPermittedRoles = ['Content_Creator_KAH', 'Site_Manager_KAH'];
    const role = this.authService.userDetail.code;
    const isAdmin = ['SUPER_ADMIN', 'ADMIN'].includes(role);
    const cmsRoles = this.getCmsRoles();
    if (isAdmin) {
      this.show = {
        adaptUi: true,
        register: true,
        backOffice: true,
        overview: true
      };
      return;
    }

    if (cmsPermittedRoles.some(permission => cmsRoles.includes(permission))) {
      this.show.adaptUi = true;
    }
    if (accessibleUserRoles.indexOf(role) != -1) {
      this.show.register = true;
      this.show.overview = true;
    }
    if (role === 'EMPLOYEE' || role === 'EMPLOYEE_CMS' || role === 'EMPLOYEE_ADMIN') {
      this.profileEditable = false;
    }
  }

  getCustomers() {
    this.loadingbpusers = true;
    this.manageUserService.getBPByUserId(this.bpUserDetails.id).subscribe((users: any) => {
      this.bpcustomers = users;
      this.loadingbpusers = false;
    });
  }

  private loadCustomers() {
    this.customers$ = concat(
      of(this.defaultOptions), // default items
      this.customerInput$.pipe(
        distinctUntilChanged(),
        tap(() => (this.customerLoading = true)),
        switchMap((term: any) => {
          if (term && term.length < 2) {
            this.customerLoading = false;
            return of(this.defaultOptions);
          }
          return this.manageUserService
            .getBusinessPartners({ search: term })
            .pipe(
              map((res: any) => {
                let data = res.data || [];
                if (this.defaultOptions[0]) {
                  data.unshift(this.defaultOptions[0]);
                }
                return res.data;
              }),
              catchError(() => of(this.defaultOptions)), // empty list on error
              tap(() => (this.customerLoading = false))
            );
        })
      )
    );
  }

  onAddCustOption($event: any) {
    if ($event.customer_id === "ALL") {
      this.registerForm.patchValue({ customers: this.defaultOptions });
    } else {
      const selectedCust = this.rf['customers'].value;
      const index = selectedCust.findIndex((o: any) => o.customer_id === "ALL");
      if (index > -1) {
        selectedCust.splice(index, 1);
        this.registerForm.patchValue({ customers: selectedCust });
      }
    }
  }

  setRegisterForm() {
    this.registerForm = this.fb.group({
      customers: [[], Validators.required],
      firstName: ["", [Validators.required]],
      lastName: ["", [Validators.required]],
      email: ["", [Validators.required, Validators.email]],
      username: [""],
      phone: ["", [Validators.pattern("^\\+[0-9]{1,3}[0-9]{5,14}$")]],
      function: ["0001", [Validators.required]],
      acceptTerms: [false, Validators.requiredTrue],
    });
  }

  get f(): { [key: string]: AbstractControl } {
    return this.profileForm.controls;
  }

  
  get cf(): { [key: string]: AbstractControl } {
    return this.changePasswordForm.controls;
  }

  get rf(): { [key: string]: AbstractControl } {
    return this.registerForm.controls;
  }

  registerUser() {
    this.isRegisterFormSubmitted = true;
    if (!this.registerForm.valid) {
      return;
    }
    const value = this.registerForm.value;
    this.registering = true;
    const payload = {
      bp_id: typeof value.customers == 'string' ? value.customers : (value.customers.bp_id || null),
      portal_user_name: value.username,
      first_name: value.firstName,
      last_name: value.lastName,
      email: value.email,
      phone: value.phone,
      is_portal_user: true,
      function_code: value.function,
    };
    this.manageUserService
      .createBPContact(payload)
      .pipe(takeUntil(this.ngUnsubscribe))
      .subscribe({
        complete: () => {
          this.cancelRegisterChanges();
          this.registering = false;
          this._snackBar.open(`Contact registered successfully!`);
        },
        error: (res) => {
          this.saving = false;
          const msg: any = res?.error?.message || null;
          if (msg) {
            if (
              msg &&
              msg.includes("unique constraint violated") &&
              msg.includes("constraint='EMAIL'")
            ) {
              this._snackBar.open("Give email address already in use.", {
                type: "Error",
              });
            } else {
              this._snackBar.open(res?.error?.message, {
                type: "Error",
              });
            }
          } else {
            this._snackBar.open("Error while processing your request.", {
              type: "Error",
            });
          }
        },
      });
  }

  saveChanges(): void {
    this.isFormSubmitted = true;
    if (!this.profileForm.valid) {
      return;
    }
    const value = this.profileForm.value;
    this.saving = true;
    this.manageUserService.updateUser(this.userDetails.id, {
      first_name: value.firstName,
      last_name: value.lastName,
      address: value.address,
      email: value.email,
      status: this.userDetails.status,
      roles: this.userDetails.roles.map((data: any) => (data.id)),
      valid_from: this.userDetails.valid_from,
      valid_to: this.userDetails.valid_to,
      country_code: value.country,
      username: value.userName,
      city: value.city,
      zip: value.zipCode,
      state: value.state,
      direct_phone: value.directPhone,
      office_phone: value.officePhone,
      cell_phone: value.cellPhone,
      other_phone: value.otherPhone,
      fax: value.fax,
      dob: value.dateOfBirth,
      gender: value.gender,
    }).subscribe((user) => {
      this.saving = false;
      this.userDetails = user.data;
      this._snackBar.open('Data updated successfully!', {
        type: 'Success',
      });
    }, (err) => {
      this.saving = false;
      this._snackBar.open(err?.error?.message || 'Error while processing your request.', {
        type: 'Error',
      });
    });
  }

  fetchUserDetails() {
    this.loadingUserDetails = true;
    const user = this.authService.getAuth();
    this.manageUserService.getUserById(user.id).subscribe(user => {
      if (user.status == 'success') {
        this.userDetails = user.data;
        this.profileForm.patchValue({
          firstName: user.data.first_name,
          lastName: user.data.last_name,
          address: user.data.contact_address,
          email: user.data.email,
          userName: user.data.username,
          city: user.data.contact_city,
          country: user.data.contact_country_code,
          zipCode: user.data.contact_zip,
          state: user.data.contact_state,
          directPhone: user.data.contact_direct_phone,
          officePhone: user.data.contact_office_phone,
          cellPhone: user.data.contact_cell_phone,
          otherPhone: user.data.contact_other_phone,
          fax: user.data.contact_fax,
          dateOfBirth: user.data.contact_dob,
          gender: user.data.contact_gender,
        });
      }
      this.loadingUserDetails = false;
    });
  }

  cancelChanges(): void {
    this.profileForm.reset();
    this.isFormSubmitted = false;
    this.isPasswordFormSubmitted = false;
  }

  cancelRegisterChanges(): void {
    this.registerForm.reset();
    this.isRegisterFormSubmitted = false;
  }

  isInvalid(controlName: string): boolean {
    const control = this.profileForm.get(controlName);
    if (!control) return false;
    return control?.invalid && (control?.dirty || control?.touched);
  }

  changePassword() {
    this.isPasswordFormSubmitted = true;
    if (!this.changePasswordForm.valid) {
      return
    }
    this.manageUserService
      .updateUserPassword(this.userDetails.id, {
        password: this.authService.encryptString(this.changePasswordForm.value.currentPassword),
      })
      .pipe(takeUntil(this.ngUnsubscribe))
      .subscribe({
        complete: () => {
          this.isPasswordFormSubmitted = false;
          this.changePasswordForm.patchValue({
            currentPassword: '',
            confirmPassword: '',
          });
          this._snackBar.open('Changes saved successfully!');
        },
        error: (err: any) => {
          this.isPasswordFormSubmitted = false;
          this._snackBar.open('Error while processing your request.', {
            type: 'Error',
          });
        },
      });
  }

  logout() {
    this.authService.doLogout();
  }

  acc_overview_btn: boolean = false;
  clickEvent() {
    this.acc_overview_btn = !this.acc_overview_btn;
  }

  saveContact(user: any) {
    const obj = {
      portal_user_name: user.contact.portal_user_name,
      first_name: user.contact.first_name,
      last_name: user.contact.last_name,
      email: user.contact.email,
      function_code: user.contact.function_code,
      contact_id: user.contact.contact_id,
      phone: user.contact.phone,
      is_portal_user: user.contact.is_portal_user
    }
    this.manageUserService.updateBPContact(obj).subscribe({
      next: (value: any) => {
        this._snackBar.open('Changes saved successfully!');
        user.editing = false;
        user.saving = false;
      },
      error: (err) => {
        this._snackBar.open('Error while processing your request.', {
          type: 'Error',
        });
      },
    });
  }

  saveAccount(user: any) {
    const obj = {
      bp_id: user.bp_id,
      is_portal_user: user.is_portal_user,
      email: user.email,
      phone: user.phone,
      bp_category: user.bp_category,
      bp_full_name: user.bp_full_name,
      bp_grouping: user.bp_grouping,
      bp_uuid: user.bp_uuid,
      org_bp_name1: user.org_bp_name1,
      org_bp_name2: user.org_bp_name2,
      org_bp_name3: user.org_bp_name3,
      org_bp_name4: user.org_bp_name4,
      search_term1: user.search_term1,
      search_term2: user.search_term2,
      portal_user_name: user.portal_user_name,
    }
    this.manageUserService.updateBPAccount(obj).subscribe({
      next: (value: any) => {
        this._snackBar.open('Changes saved successfully!');
        user.editing = false;
        user.saving = false;
      },
      error: (err) => {
        this._snackBar.open('Error while processing your request.', {
          type: 'Error',
        });
      },
    });
  }

  resetPassword(user: any) {
    const dialogRef = this.dialog.open(RegisterUserComponent);
    dialogRef.componentInstance.data = user;
    dialogRef.result.then((result) => {
    });
  }

}
