<section class="category-and-sidebar-sec">
    <div class="category-and-sidebar-body">
        <div class="category-part" *ngIf="categories.length">
            <h2 class="text-center">{{data?.Category_title}}</h2>
            <div class="category-list">
                <ng-container *ngFor="let category_item of categories">
                    <div class="category-item">
                        <a class="c-item-body" [routerLink]="['..', 'categories', category_item?.attributes?.slug]">
                            <div class="c-item-icon">
                                <img [src]="category_item?.attributes?.Thumbnail?.data?.attributes?.url" alt="" />
                            </div>
                            <div class="c-item-title">{{category_item?.attributes?.Title}}</div>
                            <div class="c-item-p">{{category_item?.attributes?.Description}}</div>
                            <div class="c-item-link">
                                {{category_item?.attributes?.Link_text}}
                                <span class="material-symbols-outlined">
                                    {{category_item?.attributes?.Link_Icon}}
                                </span>
                            </div>
                        </a>
                    </div>
                </ng-container>
            </div>
        </div>
        <!-- <ng-container *ngIf="homeSidebar?.home_sidebar_list.data.attributes.h_sidebar_list.length">
            <div class="sidebar-part">
                <ng-container *ngFor="let sidebarListBox of homeSidebar?.home_sidebar_list.data.attributes.h_sidebar_list">
                    <div class="home-sidebar-box">
                        <div class="home-sidebar-box-title">{{sidebarListBox?.sidebar_list_title}}</div>
                        <div class="home-sidebar-box-list">
                            <ul>
                                <ng-container *ngFor="let sidebarList of sidebarListBox?.sidebar_list">
                                    <li *ngIf="sidebarList?.sidebar_list_link">
                                        <a [href]="[sidebarList?.sidebar_list_link]" target="_blank">
                                            <span
                                                class="material-symbols-outlined">{{sidebarList?.sidebar_list_icon}}</span>
                                            <span class="hsb-text">{{sidebarList?.sidebar_list_name}}</span>
                                            <span class="hsb-date">{{sidebarList?.sidebar_list_date}}</span>
                                        </a>
                                    </li>
                                </ng-container>
                            </ul>
                        </div>
                    </div>
                </ng-container>
            </div>
        </ng-container> -->
    </div>
</section>