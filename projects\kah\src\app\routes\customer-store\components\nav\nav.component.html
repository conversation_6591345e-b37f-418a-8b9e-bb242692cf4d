<header class="header header_down">
    <div class="header-body">
        <div class="main-logo">
            <a routerLink="/store/home"><img [src]="data?.Logo.data.attributes.url" class="img-fluid" alt="" /></a>
        </div>
        <div class="main-menu" [ngClass]="mmb_btn ? 'show' : 'hide'">
            <ul>
                <ng-container *ngFor="let menuItem of mainmenuData">
                    <li *ngIf="menuItem?.path">
                        <a href="javascript: void(0)" [routerLink]="['/store']" [target]="menuItem?.target ?? '_self'"
                            *ngIf="menuItem.path == '/store'">{{menuItem?.title}}
                            <span class="material-symbols-outlined"
                                *ngIf="menuItem?.items?.length">keyboard_arrow_down</span>
                        </a>
                        <a [href]="menuItem?.type == 'WRAPPER' ? menuItem.wrapper_url : menuItem?.path" [target]="menuItem?.target ?? '_self'" *ngIf="menuItem.path != '/store'">{{menuItem?.title}}
                            <span class="material-symbols-outlined"
                                *ngIf="menuItem?.items?.length">keyboard_arrow_down</span>
                        </a>
                        <button type="button" *ngIf="menuItem?.items?.length"
                            class="m-dropdown-arrow p-0 d-flex align-items-center justify-content-center">
                            <span class="material-symbols-outlined" (click)="menuItem.menu_toggle = !menuItem.menu_toggle"
                                [ngClass]="menuItem.menu_toggle ? 'up' : 'down'">keyboard_arrow_down</span>
                        </button>
                        <div class="sub-menu" *ngIf="menuItem?.items?.length"
                            [ngClass]="menuItem.menu_toggle ? 'show' : 'hide'">
                            <ul>
                                <ng-container *ngFor="let submenuItem of menuItem?.items">
                                    <li *ngIf="submenuItem?.path" class="submenu-li">
                                        <a [href]="submenuItem?.type == 'WRAPPER' ? submenuItem.wrapper_url : submenuItem?.path" [target]="submenuItem?.target ?? '_self'">
                                            {{submenuItem?.title}}
                                            <span class="material-symbols-outlined"
                                                *ngIf="submenuItem?.items?.length">keyboard_arrow_down</span>
                                        </a>
                                        <button type="button" *ngIf="submenuItem?.items?.length"
                                            class="m-dropdown-arrow p-0 d-flex align-items-center justify-content-center">
                                            <span class="material-symbols-outlined" (click)="submenuItem.submenu_toggle = !submenuItem.submenu_toggle"
                                                [ngClass]="submenuItem.submenu_toggle ? 'up' : 'down'">keyboard_arrow_down</span>
                                        </button>
                                        <div class="sub-sub-menu" *ngIf="submenuItem?.items?.length"
                                            [ngClass]="submenuItem.submenu_toggle ? 'show' : 'hide'">
                                            <ul>
                                                <ng-container
                                                    *ngFor="let subsubmenuItem of submenuItem?.items">
                                                    <li *ngIf="subsubmenuItem?.path">
                                                        <a [href]="[subsubmenuItem?.path]" [target]="submenuItem?.target ?? '_self'">
                                                            {{subsubmenuItem?.title}}
                                                        </a>
                                                    </li>
                                                </ng-container>
                                            </ul>
                                        </div>
                                    </li>
                                </ng-container>
                            </ul>
                        </div>
                    </li>
                </ng-container>
            </ul>
        </div>
        <div class="mobile-menu-btn-sec position-relative d-none align-items-center justify-content-between">
            <button type="button"
                class="mmb-btn d-flex m-0 p-0 position-relative align-items-center justify-content-center"
                (click)="clickEvent()" [ngClass]="mmb_btn ? 'close' : 'open'">
                <span class="material-symbols-outlined mmb-menu-bars">menu</span>
                <span class="material-symbols-outlined mmb-menu-close">close</span>
            </button>
        </div>
        <div class="h-right">
            <div ngbDropdown class="h-user">
                <button type="button" class="user-link" id="dropdownBasic1" ngbDropdownToggle>
                    <div class="user-link-icon"><span class="material-symbols-outlined">person</span></div>
                    <span class="material-symbols-outlined arrow-down">keyboard_arrow_down</span>
                </button>
                <div ngbDropdownMenu aria-labelledby="dropdownBasic1" class="user-popup">
                    <ng-container *ngFor="let headerUserDetails of links">
                        <a [href]="[headerUserDetails?.header_user_url]" [target]="headerUserDetails?.header_user_url == cmsUrl ? '_blank' : '_self'">
                            <span class="material-symbols-outlined">{{headerUserDetails?.header_user_icon}}</span>
                            {{headerUserDetails?.header_user_text}}
                        </a>
                    </ng-container>
                    <a (click)="logout()"><span class="material-symbols-outlined">logout</span>Logout</a>
                </div>
            </div>
        </div>
    </div>
</header>