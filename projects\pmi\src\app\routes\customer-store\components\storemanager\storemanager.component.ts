import { Component, Input, OnInit } from '@angular/core';
import { StoreFrontAPIConstant } from '../../constants/api.constants';

@Component({
  selector: 'app-storemanager',
  templateUrl: './storemanager.component.html',
  styleUrls: ['./storemanager.component.scss']
})
export class StoremanagerComponent implements OnInit {

  @Input() data: any;

  imgPath: string = StoreFrontAPIConstant.IMG_URL;

  constructor() { }

  ngOnInit(): void { }

}
