# Starter pipeline
# Start with a minimal pipeline that you can customize to build and deploy your code.
# Add steps that build, run tests, deploy, and more:
# https://aka.ms/yaml

resources:
  containers:
  - container: cfcli
    image: 'ajayrdockerpika/node16cfcli:v1'
    options: --user 0:0 --privileged

trigger:
- dev

pool:
  vmImage: ubuntu-latest

variables:
- group: CF_MTY_SecretKeys

stages:
- stage: build
  displayName: Build solution
  jobs:
    - job: build
      steps:
        - script: cd '$(System.DefaultWorkingDirectory)'
        - script: npm install --legacy-peer-deps
        - script: npm run build-all

- stage: deploy
  displayName: Deployment to BTP
  jobs:
    - job: deploy
      pool:
        vmImage: 'ubuntu-latest'
      container: cfcli
      steps:
        - script: cd '$(System.DefaultWorkingDirectory)/dist/ppfe'
        - script: cf login -a $(API_ENDPOINT) -u $(USER) -p $(PASSWORD) -o "MTY Franchising Inc._snjya-portal-development-3a3snuj7" -s $(SPACE_DEV)
        - script: cf push  
