import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { StoreFrontAPIConstant } from '../constants/api.constants';
import { ComponentNameConstants } from '../constants/components.contants';
import { of, tap } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class HomeService {

  cache: { [key in string]: any } = {};

  constructor(private http: HttpClient) { }

  getHomePageDetails() {
    const url = `${StoreFrontAPIConstant.HOME_API_DETAILS}?populate=deep`;
    if (this.cache[url]) {
      return of(this.cache[url]);
    }
    return this.http.get<any>(
      url
    ).pipe(
      tap((res) => {
        this.cache[url] = res;
      })
    );
  }

  getLoginPageDetails() {
    return this.http.get<any>(
      `${StoreFrontAPIConstant.KAH_LOGIN_API_DETAILS}?populate=deep`
    );
  }

  getMainmenuDetails() {
    return this.http.get<any>(
      `${StoreFrontAPIConstant.MAIN_MENU_API_DETAILS}`
    );
  }

  getDataByComponentName(data: any[], componentName: ComponentNameConstants) {
    const components = data.filter((item) => item.__component === componentName);
    return components.length == 1 ? components[0] : components;
  }

  fetchCategories() {
    return this.http.get<any>(
      `${StoreFrontAPIConstant.Categories}?populate=deep,2`
    );
  }

}
