import { Component, OnInit } from '@angular/core'
import { HomeService } from './services/home.service';
import { ComponentNameConstants } from './constants/components.contants';

@Component({
    selector: 'app-customer-store',
    templateUrl: './customer-store.component.html',
    styleUrls: ['./customer-store.component.scss']
})
export class CustomerStoreComponent implements OnInit {

    headerData: any;
    footerLinksdata: any = {};
    footerData: any = {};
    loading = false;

    constructor(public service: HomeService) { }

    ngOnInit(): void {
        this.fetchData();
    }

    fetchData() {
        this.loading = true;
        this.service.getHomePageDetails().subscribe({
            next: (res: any) => {
                this.headerData = this.service.getDataByComponentName(res?.data?.attributes?.Body || [], ComponentNameConstants.Header);
                this.footerData = this.service.getDataByComponentName(res?.data?.attributes?.Body || [], ComponentNameConstants.Footer);
                this.footerLinksdata = this.service.getDataByComponentName(res?.data?.attributes?.Body || [], ComponentNameConstants.footerLinks);
                this.loading = false;
            }
        });
    }

}
