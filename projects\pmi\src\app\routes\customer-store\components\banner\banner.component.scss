/*----HOME BANNER----*/
.home-banner-sec {
    margin: 0;
    position: relative;

    .home-banner-img {
        margin: 0;
        padding: 0;
        position: relative;

        .home-b-img {
            margin: 0;
            padding: 0;
            position: relative;
            display: flex;
            width: 100%;
            height: 650px;
            background-size: cover !important;
            cursor: pointer;

            &::before {
                position: absolute;
                content: '';
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgb(0 0 0 / 20%);
            }
        }
    }

    .home-banner-body {
        margin: auto;
        padding: 0 30px;
        position: absolute;
        top: 0;
        bottom: 0;
        left: 0;
        right: 0;
        width: 100%;
        height: fit-content;
        max-width: 1500px;
        display: flex;
        flex-flow: column;
        gap: 32px 0;
        z-index: 10;

        h1 {
            margin: 0;
            padding: 0;
            position: relative;
            display: flex;
            line-height: 32px;
            font-size: var(--brand-font-size-5);
            color: var(--brand-color-white);
            text-shadow: 0 2px 3px rgb(0 0 0 / 16%);
            font-weight: var(--brand-font-weight-bold);
        }

        .h-banner-search-form,
        .h-banner-search-form form {
            position: relative;
        }

        .h-banner-search-form {
            margin: 0;
            padding: 0;
            max-width: 540px;
            width: 100%;

            .form-group {
                margin: 0;
                padding: 0;
                position: relative;
                width: 100%;

                .form-control {
                    margin: 0;
                    padding: 0 150px 0 15px;
                    height: 56px;
                    font-size: var(--brand-font-size-0-9);
                    border-radius: 12px;
                    border: 1px solid var(--brand-color-white) !important;
                    font-weight: var(--brand-font-weight-light);
                    box-shadow: 0 2px 3px rgba(0, 0, 0, 0.10) !important;

                    &::placeholder {
                        color: #9fa1a7;
                    }
                }
            }

            .dd {
                position: absolute;
                z-index: 1;
                height: 100%;
                padding: 4px;

                >button {
                    height: 48px;
                    max-width: 160px;
                    width: 160px;
                    font-size: 12px;
                    min-width: 160px;
                }

                .dropdown-menu.show {
                    max-height: 300px;
                    overflow: auto;
                }
            }

            .h-banner-search-btn {
                margin: auto;
                padding: 0;
                position: absolute;
                right: 5px;
                top: 0;
                bottom: 0;
                height: 100%;

                .form-control {
                    padding: 0 12px;
                    height: 100%;
                    font-size: var(--brand-font-size-0-9);
                    background: var(--brand-color-black);
                    border-radius: 0;
                    color: var(--bs-white);
                    border-right: 1px solid var(--bs-white) !important;
                    appearance: auto;
                }

                button.btn.btn-primary {
                    margin: 0;
                    padding: 10px 20px;
                    font-size: var(--brand-font-size-0-9);
                    background: var(--brand-color-black);
                    border: none;
                    font-weight: var(--brand-font-weight-normal);
                    gap: 0 4px;
                    height: 48px;
                    border-radius: 8px;
                    opacity: 1;

                    img {
                        width: 24px;
                        filter: invert(1) contrast(10);
                    }
                }
            }
        }

    }
}

/*----HOME BANNER----*/


/*-------MEDIA SCREEN 1440px-------*/
@media only screen and (max-width: 1440px) {
    .home-banner-sec {
        .home-banner-img {
            .home-b-img {
                height: 600px;
            }
        }

        .home-banner-body {
            max-width: 1200px;
            gap: 20px 0;

            h1 {
                font-size: var(--brand-font-size-4);
            }

        }
    }
}

/*-------MEDIA SCREEN 1440px-------*/

/*-------MEDIA SCREEN 1130px-------*/
@media only screen and (max-width: 1130px) {
    .home-banner-sec {
        .home-banner-img {
            .home-b-img {
                height: 500px;
            }
        }

        .home-banner-body {
            align-items: center;

        }
    }
}

/*-------MEDIA SCREEN 1130px-------*/

/*-------MEDIA SCREEN 576px-------*/
@media only screen and (max-width: 576px) {
    .home-banner-sec {
        .home-banner-body {
            h1 {
                font-size: var(--brand-font-size-3);
            }
        }

        .home-banner-img {
            .home-b-img {
                height: 400px;
            }
        }
    }
}

/*-------MEDIA SCREEN 576px-------*/

/*-------MEDIA SCREEN 414px-------*/
@media only screen and (max-width: 480px) {
    .home-banner-sec {
        .home-banner-body {
            padding: 0 20px;

            .h-banner-search-form {
                .form-group {
                    .form-control {
                        padding: 0 62px 0 168px;

                    }
                }

                .dd {

                    >button {
                        max-width: 152px;
                        width: 152px;
                        min-width: 152px;
                    }
                }

                .h-banner-search-btn {
                    button {
                        font-size: 0 !important;
                        gap: 0 !important;
                        padding: 6px 12px !important;
                    }
                }
            }
        }
    }
}

/*-------MEDIA SCREEN 414px-------*/