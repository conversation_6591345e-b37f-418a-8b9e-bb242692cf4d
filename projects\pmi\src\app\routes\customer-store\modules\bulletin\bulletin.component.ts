import { Component, OnInit } from '@angular/core';
import { OwlOptions } from 'ngx-owl-carousel-o';

@Component({
  selector: 'pmi-bulletin',
  templateUrl: './bulletin.component.html',
  styleUrls: ['./bulletin.component.scss']
})
export class BulletinComponent implements OnInit {

  ngOnInit(): void {

  }

  navSpeed = 2000;

  setNavSpeed(data: any) {
    this.navSpeed = data.Banner_Timer || this.navSpeed;
    this.customOptions = {
      ...this.customOptions,
      navSpeed: this.navSpeed,
      autoplayTimeout: this.navSpeed,
      autoplaySpeed: this.navSpeed,
    };
  }

  
  customOptions: OwlOptions = {
    loop: true,
    autoplay: true,
    dots: false,
    nav: false,
    navSpeed: this.navSpeed,
    autoplayTimeout: this.navSpeed,
    autoplaySpeed: this.navSpeed,
    animateOut: 'fadeOut',
    navText: ["<img src='/assets/images/arrow_back.svg' />", "<img src='/assets/images/arrow_forward.svg' />"],
    responsive: {
      0: {
        items: 1,
      },
      600: {
        items: 1,
      },
      1000: {
        items: 1,
      },
    },
  };

}
