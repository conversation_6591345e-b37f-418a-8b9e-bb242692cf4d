/*------KAH LIST BOX------*/
.kah-list-sec {
    margin: 0;
    padding: 50px 0;
    min-height: 600px;

    .kah-list-body {
        margin: 0 auto;
        padding: 0 30px;
        max-width: 1500px;
        width: 100%;

        .kah-list {
            grid-template-columns: repeat(auto-fill, minmax(23%, 1fr));
            gap: 0 30px;

            .kah-list-title {
                margin: 0 0 15px 0;
                padding: 0 0 15px 0;
                font-size: var(--brand-font-size-22);
                font-weight: var(--brand-font-weight-medium);
                color: var(--brand-color-primary);
                gap: 0 8px;
                border-bottom: 2px solid var(--brand-accordian-button-color);

                span {
                    font-size: var(--brand-font-size-3);
                    color: var(--brand-color-secondary);
                }
            }

            ul {
                gap: 15px;

                a {
                    position: relative;
                    display: flex;
                    gap: 0 8px;

                    span {
                        font-weight: var(--brand-font-weight-bolder);
                        color: var(--brand-color-primary);
                    }

                    .kah-list-text {
                        font-weight: var(--brand-font-weight-semi-bold);
                        font-size: var(--brand-font-size-1);
                        color: var(--brand-color-secondary-neutral-charcoal);
                        line-height: 22px;
                    }

                    &:hover .kah-list-text {
                        text-decoration: underline !important;
                    }
                }
            }
        }
    }
}

/*------KAH LIST BOX------*/


/*-------MEDIA SCREEN 1440px-------*/
@media only screen and (max-width: 1440px) {
    .kah-list-sec {
        min-height: 400px;

        .kah-list-body {
            max-width: 1200px;

            .kah-list-box {

                .kah-list-title {
                    margin: 0 0 12px 0;
                    padding: 0 0 12px 0;
                    font-size: var(--brand-font-size-1-125);
                    gap: 0 8px;

                    span {
                        font-size: var(--brand-font-size-22);
                    }
                }

                ul {
                    gap: 12px;

                    a {
                        span {
                            font-size: var(--brand-font-size-1-25);
                        }

                        .kah-list-text {
                            font-size: var(--brand-font-size-0-875);
                            line-height: 18px;
                        }
                    }
                }
            }

        }
    }
}

/*-------MEDIA SCREEN 1440px-------*/

/*-------MEDIA SCREEN 576px-------*/
@media only screen and (max-width: 576px) {
    .kah-list-sec {
        .kah-list-body {
            max-width: 1200px;

            .kah-list {
                grid-template-columns: repeat(auto-fill, minmax(100%, 1fr));
                gap: 30px 0;
            }
        }
    }
}

/*-------MEDIA SCREEN 576px-------*/