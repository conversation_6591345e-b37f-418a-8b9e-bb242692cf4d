<section class="home-banner-sec">
    <div class="home-banner-img">
        <owl-carousel-o [options]="customOptions">
            <ng-container *ngFor="let slide of data?.banner_imgs">
                <ng-template carouselSlide [id]="slide.id.toString()">
                    <div class="home-b-img"
                        style="background: url('{{slide.banner_imgs.data.attributes.url}}') center center no-repeat;"
                        (click)="openUrl(slide)"></div>
                </ng-template>
            </ng-container>
        </owl-carousel-o>
    </div>
    <div class="home-banner-body">
        <h1>{{data?.Banner_Title}}</h1>
        <div class="h-banner-search-form">
            <form>
                <!-- <div ngbDropdown class="d-inline-block dd">
                    <button type="button" class="btn btn-outline-secondary text-truncate" id="dropdownBasic1"
                        ngbDropdownToggle [title]="selectedCategory ? selectedCategory.name : 'Categories'">
                        {{ selectedCategory ? selectedCategory.name : 'Select Category'}}
                    </button>
                    <div ngbDropdownMenu aria-labelledby="dropdownBasic1">
                        <button type="button" class="text-truncate d-flex align-items-center justify-content-center"
                            [title]="category.name" ngbDropdownItem *ngFor="let category of categories"
                            (click)="selectCategory(category)">{{ category.name
                            }}</button>
                    </div>
                </div> -->
                <div class="form-group">
                    <input type="search" class="form-control" placeholder="Enter any keyword"
                        [ngModelOptions]="{standalone: true}" [class.is-invalid]="searchFailed" [(ngModel)]="model"
                        (keyup.enter)="openArticle()">
                </div>
                <div class="h-banner-search-btn d-flex align-items-center">
                    <button type="button" class="btn btn-primary d-flex align-items-center justify-content-center"
                        [disabled]="!model" (click)="openArticle()">
                        <span class="material-symbols-outlined">{{data?.Banner_search_icon}}</span>
                        {{data?.Banner_search_text}}</button>
                </div>
            </form>
        </div>
    </div>
</section>
<ng-template #rt let-r="result" let-t="term">
    <ngb-highlight [result]="r.name" [term]="t"></ngb-highlight>
</ng-template>