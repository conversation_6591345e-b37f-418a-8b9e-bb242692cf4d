import { NgModule } from "@angular/core";
import { RouterModule, Routes } from "@angular/router";
import { ActivationRequestGuard } from "../../core/authentication/auth.guard";
// import { DashboardGuard } from "./guard/dashboard.guard";

const routes: Routes = [
  { path: "home", loadChildren: () => import("./modules/home/<USER>").then((mod) => mod.HomeModule), },
  { path: "articles", loadChildren: () => import("./modules/artical-details/artical-details.module").then((mod) => mod.ArticalDetailsModule), },
  { path: "my-account", loadChildren: () => import("./modules/my-account/my-account.module").then((mod) => mod.MyAccountModule), },
  { path: "categories", loadChildren: () => import("./modules/categories/categories.module").then((mod) => mod.CategoriesModule), },
  { path: "article-search", loadChildren: () => import("./modules/search-articles/search-articles.module").then((mod) => mod.SearchArticlesModule), },
  { path: "bulletin", loadChildren: () => import("./modules/bulletin/bulletin.module").then((mod) => mod.BulletinsModule), },
  { path: "campaign-center", loadChildren: () => import("./modules/campaign-center/campaign-center.module").then((mod) => mod.CampaignCenterModule), canActivate: [ActivationRequestGuard] },
  { path: "my-activation", loadChildren: () => import("./modules/my-activation/my-activation.module").then((mod) => mod.MyActivationModule), canActivate: [ActivationRequestGuard] },
  { path: "dashboard", redirectTo: "home", pathMatch: "full" },
  { path: "", redirectTo: "home", pathMatch: "full" },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class CustomerStoreRoutingModule { }
