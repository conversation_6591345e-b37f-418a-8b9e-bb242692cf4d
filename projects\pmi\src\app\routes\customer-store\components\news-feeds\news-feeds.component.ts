import { Component, Input } from '@angular/core';
import { StoreFrontAPIConstant } from '../../constants/api.constants';
import { OwlOptions } from 'ngx-owl-carousel-o';

@Component({
  selector: 'pmi-news-feeds',
  templateUrl: './news-feeds.component.html',
  styleUrls: ['./news-feeds.component.scss']
})
export class NewsFeedsComponent {

  @Input() data: any;

  imgPath: string = StoreFrontAPIConstant.IMG_URL;

  constructor() { }

  ngOnInit(): void { }

  customOptions: OwlOptions = {
    loop: true,
    autoplay: true,
    dots: false,
    nav: true,
    navSpeed: 3000,
    autoplayTimeout: 3000,
    autoplaySpeed: 3000,
    animateOut: 'fadeOut',
    navText: ["<img src='/assets/images/arrow_back.svg' />", "<img src='/assets/images/arrow_forward.svg' />"],
    responsive: {
      0: {
        items: 1
      },
      400: {
        items: 1
      },
      740: {
        items: 1
      },
      940: {
        items: 1
      }
    },
  }

}
