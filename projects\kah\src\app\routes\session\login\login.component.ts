import { Component, Inject, Input } from '@angular/core';
import { FormBuilder, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { AuthService } from 'ng-snjya';
import { catchError, of } from 'rxjs';
import { ENV_CONFIG, ROUTER_URL } from '../../ng-snjya.config.token';
import { RolesType } from '../../../core/constants/constants';
import { StoreFrontAPIConstant } from '../../customer-store/constants/api.constants';
import { OwlOptions } from 'ngx-owl-carousel-o';
import { HomeService } from '../../customer-store/services/home.service';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { BrandSelectionComponent } from '../brand-selection/brand-selection.component';
import { AuthWatchService } from '../services/auth-watch.service';
import { ComponentNameConstants } from '../../customer-store/constants/components.contants';
import { HttpClient } from '@angular/common/http';


@Component({
  selector: 'snjya-login',
  templateUrl: './login.component.html',
  styleUrls: ['./login.component.scss'],
})
export class LoginComponent {
  public loginData: any = {};
  showFranchiseesForm: boolean = false;
  showCorporateForm: boolean = false;
  showPassword = false;
  navSpeed = 2000;

  public API_ENDPOINT: any = null;
  public isSubmitting = false;
  public loginForm = this.fb.nonNullable.group({
    email: ['', [Validators.required]],
    password: ['', [Validators.required]],
    rememberMe: [false],
    selectedTenant: ['MTY_GROUP'],
  });
  public rorigin = window.location.origin;
  public errMsg: any = null;
  public tenantArr = [
    {
      name: 'MTY Group',
      value: 'MTY_GROUP',
    },
    {
      name: 'Kahala Brands',
      value: 'KAHALA_BRANDS',
    },
    {
      name: 'Wetzels Pretzels',
      value: 'WETZELS_PRETZELS',
    },
    {
      name: 'BBQ / Famous Daves',
      value: 'BBQ_FAMOUS_DAVES',
    },
    {
      name: 'Papa Murphys',
      value: 'PAPA_MURPHYS',
    },
  ];

  constructor(
    private fb: FormBuilder,
    public router: Router,
    private auth: AuthService,
    private http: HttpClient,
    @Inject(ENV_CONFIG) private environment: any,
    @Inject(ROUTER_URL) private router_url: any,
    public Homeservice: HomeService,
    private dialog: NgbModal,
    private authWatchService: AuthWatchService,
    public service: HomeService
  ) {
    this.API_ENDPOINT = this.environment.apiEndpoint;
  }

  get email() {
    return this.loginForm.get('email')!;
  }

  get password() {
    return this.loginForm.get('password')!;
  }

  get rememberMe() {
    return this.loginForm.get('rememberMe')!;
  }

  get selectedTenant() {
    return this.loginForm.get('selectedTenant')!;
  }

  openBrandSelectionDialog() {
    const dialogRef = this.dialog.open(BrandSelectionComponent, {
      size: 'lg'
    });
    dialogRef.result.then((result) => {
    });
  }

  login() {
    this.isSubmitting = true;
    this.auth
      .login(this.email.value, this.auth.encryptString(this.password.value))
      .pipe(catchError((err) => of(err.error)))
      .subscribe((res: any) => {
        this.isSubmitting = false;
        if (res === 'success') {
          if (false) {
            this.openBrandSelectionDialog();
            return;
          }
          this.auth.cmsLogin(this.email.value, this.password.value).subscribe((res) => {
            return this.http
              .get<any>(StoreFrontAPIConstant.CMS_ME, {
                headers: {
                  Authorization: 'Bearer ' + res.token,
                },
              }).subscribe((res) => {
                try {
                  const cmsPermittedRoles = ['Super Admin', 'Content_Creator_KAH', 'Site_Manager_KAH'];
                  const cmsRoles = (res.data.roles || []).map((role: any) => role.name);
                  if (
                    cmsPermittedRoles.some((permission) =>
                      cmsRoles.includes(permission)
                    )
                  ) {
                    const userInfo = localStorage.getItem('userInfo');
                    if (userInfo) {
                      const obj = JSON.parse(userInfo);
                      obj.userRoles = res.data.roles;
                      localStorage.setItem('userInfo', JSON.stringify(obj));
                    }
                  } else {
                    localStorage.removeItem('userInfo');
                    localStorage.removeItem('jwtToken');
                  }
                } catch (e) {
                  console.log(e);
                }
              });
          });
          const redirectUrl = localStorage.getItem('redirectUrl');
          if (redirectUrl) {
            localStorage.removeItem('redirectUrl');
            this.router.navigateByUrl(redirectUrl);
          } else {
            this.checkRoleType();
          }
          this.authWatchService.watch();
        } else if (res?.status === 'error') {
          this.errMsg = res.message;
        }
      });
  }

  private checkRoleType() {
    switch (this.auth.role) {
      case RolesType.STOREFRONT:
        this.router.navigate([`/${this.router_url.STOREFRONT}/home`]);
        break;
      case RolesType.CUST_SERVICE:
        this.router.navigate([
          `/${this.router_url.STOREFRONT}/customer-services`,
        ]);
        break;
      case RolesType.SALES:
        this.router.navigate([`/${this.router_url.STOREFRONT}/sales`]);
        break;
      case RolesType.VENDOR:
        this.router.navigate([`/${this.router_url.STOREFRONT}/vendor`]);
        break;
      default:
        this.router.navigate([`/${this.router_url.BACKOFFICE}/dashboard`]);
    }
  }

  reset() {
    this.loginForm.reset();
    this.errMsg = null;
  }

  @Input() data: any;
  @Input() footerData: any = {};
  loading = false;

  imgPath: string = StoreFrontAPIConstant.IMG_URL;

  ngOnInit(): void {
    this.getLoginPageDetails();
    this.fetchData();
  }

  fetchData() {
    this.loading = true;
    this.service.getHomePageDetails().subscribe({
      next: (res: any) => {
        this.footerData = this.service.getDataByComponentName(res?.data?.attributes?.Body || [], ComponentNameConstants.Footer);
        this.loading = false;
      }
    });
  }

  getLoginPageDetails() {
    this.Homeservice.getLoginPageDetails().subscribe({
      next: (res: any) => {
        console.log(res);
        this.loginData = res?.data?.attributes?.Login_Page_Kah || [];
        this.navSpeed = this.loginData.Banner_Timer || this.navSpeed;
        setTimeout(() => {
          this.loginOptions = {
            ...this.loginOptions,
            navSpeed: this.navSpeed,
            autoplayTimeout: this.navSpeed,
            autoplaySpeed: this.navSpeed,
          };
        });
      }
    });
  }

  loginOptions: OwlOptions = {
    loop: true,
    autoplay: true,
    dots: false,
    nav: false,
    navSpeed: this.navSpeed,
    autoplayTimeout: this.navSpeed,
    autoplaySpeed: this.navSpeed,
    animateOut: 'fadeOut',
    navText: ["<img src='/assets/images/arrow_back.svg' />", "<img src='/assets/images/arrow_forward.svg' />"],
    responsive: {
      0: {
        items: 1,
      },
      600: {
        items: 1,
      },
      1000: {
        items: 1,
      },
    },
  };

  // toggleLoginForm() {
  //   this.showLoginForm = !this.showLoginForm;
  // }

  toggleFranchiseesForm() {
    this.loginForm.reset();
    this.showPassword = false;
    this.showFranchiseesForm = !this.showFranchiseesForm;
    this.showCorporateForm = false; // Ensure only one form is shown at a time
  }

  toggleCorporateForm() {
    this.loginForm.reset();
    this.showPassword = false;
    this.showCorporateForm = !this.showCorporateForm;
    this.showFranchiseesForm = false; // Ensure only one form is shown at a time
  }
}
