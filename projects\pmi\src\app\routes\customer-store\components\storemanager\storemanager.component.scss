/*----STORE MANAGER SEC----*/
.store-manager-sec {
    margin: 0;
    padding: 54px 0;
    position: relative;
    height: 450px;

    .store-m-body {
        margin: 0 auto;
        padding: 0 30px;
        max-width: 1500px;
        width: 100%;
        position: relative;

        h2 {
            margin: 0 0 46px 0;
            padding: 0;
            position: relative;
            line-height: 42px;
            font-size: var(--brand-font-size-4);
            color: var(--brand-color-secondary-neutral-charcoal);
            font-weight: var(--brand-font-weight-bold);
        }

        .store-m-list {
            ul {
                margin: 0;
                padding: 0;
                position: relative;
                display: grid;
                grid-template-columns: repeat(auto-fill, minmax(30%, 1fr));
                gap: 15px;

                a {
                    position: relative;
                    display: flex;
                    gap: 0 8px;

                    span {
                        font-weight: var(--brand-font-weight-bolder);
                        color: var(--brand-color-primary);
                    }

                    .store-m-text {
                        font-weight: var(--brand-font-weight-normal);
                        font-family: var(--brand-font-family-secondary);
                        font-size: var(--brand-font-size-1);
                        color: var(--brand-color-secondary-neutral-charcoal);
                        line-height: 22px;
                        text-transform: capitalize;
                    }

                    &:hover .store-m-text {
                        text-decoration: underline !important;
                    }
                }
            }
        }
    }
}

/*----STORE MANAGER SEC----*/

/*-------MEDIA SCREEN 1440px-------*/
@media only screen and (max-width: 1440px) {
    .store-manager-sec {
        .store-m-body {
            max-width: 1200px;

            h2 {
                margin: 0 0 40px 0;
                position: relative;
                line-height: 36px;
                font-size: var(--brand-font-size-3);
            }

            .store-m-list {
                ul {
                    gap: 12px;

                    a {
                        span {
                            font-size: var(--brand-font-size-1-25);
                        }

                        .store-m-text {
                            font-size: var(--brand-font-size-0-875);
                            line-height: 18px;
                        }
                    }
                }
            }
        }
    }
}

/*-------MEDIA SCREEN 1440px-------*/

/*-------MEDIA SCREEN 576px-------*/
@media only screen and (max-width: 576px) {
    .store-manager-sec {
        padding: 40px 0;
        height: fit-content;

        .store-m-body {
            h2 {
                line-height: 30px;
                font-size: var(--brand-font-size-26);
            }

            .store-m-list ul {
                grid-template-columns: repeat(auto-fill, minmax(100%, 1fr));
            }
        }
    }
}

/*-------MEDIA SCREEN 576px-------*/