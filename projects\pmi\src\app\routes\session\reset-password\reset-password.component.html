<div class="flex-grow-1 d-flex flex-column justify-content-between h-100 p-3">
    <a class="navbar-brand" href="#"><img src="assets/images/logo/logo-red.png" class="img-fluid"></a>
    <div class="forgot-password-box">
        <h4 class="mb-4">Change Your Password</h4>
        <div class="p-3 form-container">
            <form [formGroup]="form" class="p-4">
                <div class="form-group mb-4 required">
                    <label class="mb-2">Password</label>
                    <input type="password" formControlName="password" class="form-control mt-1 mb-2"
                        [ngClass]="{ 'is-invalid': submitted && f['password'].errors }" />
                    <div *ngIf="submitted && f['password'].errors" class="invalid-feedback">
                        <div *ngIf="f['password'].errors['required']">
                            This field is required</div>
                        <div *ngIf="f['password'].errors['minlength']">
                            Must be at least 8 characters</div>
                        <div *ngIf="f['password'].errors['hasNumber']">
                            Must contain at least one number</div>
                        <div *ngIf="f['password'].errors['hasCapitalCase']">
                            Must contain at least one Letter in Capital Case</div>
                        <div *ngIf="f['password'].errors['hasSmallCase']">
                            Must contain at least one Letter in Small Case</div>
                        <div *ngIf="f['password'].errors['hasSpecialCharacters']">
                            Must contain at least one Special Character</div>
                    </div>
                </div>
                <div class="form-group mb-4 required">
                    <label class="mb-2">Confirm Password</label>
                    <input type="password" formControlName="passwordConfirm" class="form-control mt-1 mb-2"
                        [ngClass]="{ 'is-invalid': submitted && f['passwordConfirm'].errors }" />
                    <div *ngIf="submitted && f['passwordConfirm'].errors" class="invalid-feedback">
                        <div *ngIf="f['passwordConfirm'].errors['required']">
                            This field is required</div>
                        <div *ngIf="f['passwordConfirm'].errors['confirmedValidator']">
                            Passwords must match
                        </div>
                    </div>
                </div>
                <div class="button-section mt-4 flex-column">
                    <button type="submit" class="btn btn-light w-100 btn-login" [disabled]="saving"
                        (click)="onSubmit()">Change
                        Password</button>
                    <span class="form-text hint decoration-none text-center" role="button" [routerLink]="'../login'">
                        Back to Login</span>
                </div>
            </form>
        </div>
    </div>
    <div class="col-sm-12 d-flex flex-column flex-xl-row border-top align-items-center justify-content-between pt-3">
        <div class="copy-right fs-7">
            {{footerData?.footer_copyright}}
        </div>
        <ul class="nav-link mb-0 d-flex flex-row gap-3 fs-7">
            <ng-container *ngFor="let bottomFooterLinks of footerData?.Bottom_Footer_Links">
                <li *ngIf="bottomFooterLinks?.footer_link">
                    <a [href]="[bottomFooterLinks?.footer_link]" target="_blank">{{bottomFooterLinks?.footer_link_name}}</a>
                </li>
            </ng-container>
        </ul>
    </div>
</div>