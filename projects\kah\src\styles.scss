@import "./assets/theme/index.scss";

.home-banner-img {
    .owl-carousel {

        // .owl-item {
        //     &:before {
        //         position: absolute;
        //         content: "";
        //         left: 0;
        //         top: 0;
        //         width: 100%;
        //         height: 100%;
        //         background: rgb(0 0 0 / 15%);
        //         z-index: 2;
        //     }
        // }

        .owl-nav {
            position: absolute;
            padding: 0 50px;
            top: 0;
            bottom: 0;
            margin: auto;
            height: fit-content;
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: space-between;

            &>div {
                margin: 0;
                padding: 0;
                position: relative;
                display: flex;
                align-items: center;
                width: 40px;
                height: 40px;
                border-radius: 50px;
                justify-content: center;
                color: #FFF;
                background: rgb(0 0 0 / 18%);

                img {
                    width: 18px;
                }

                &:hover {
                    background: #2a606a57;
                }
            }
        }
    }
}

.owl-carousel .owl-dots.disabled,
.owl-carousel .owl-nav.disabled {
    display: none !important;
}

.flex-basis-0 {
    flex-basis: 0;
}


/*-------MEDIA SCREEN 768px-------*/
@media only screen and (max-width: 768px) {
    .home-banner-img {
        .owl-carousel {
            .owl-nav {
                display: none !important;
            }
        }
    }
}

/*-------MEDIA SCREEN 768px-------*/
.cursor-pointer {
    cursor: pointer;
}