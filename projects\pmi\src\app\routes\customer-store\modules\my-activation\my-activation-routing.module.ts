import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { MyActivationComponent } from './my-activation.component';
import { MyActivationDetailsComponent } from './my-activation-details/my-activation-details.component';

const routes: Routes = [{
  path: '',
  component: MyActivationComponent
},
{
  path: ':id',
  component: MyActivationDetailsComponent
}];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class MyActivationRoutingModule { }
