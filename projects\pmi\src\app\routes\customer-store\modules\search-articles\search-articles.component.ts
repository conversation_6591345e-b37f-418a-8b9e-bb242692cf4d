import { HttpClient } from '@angular/common/http';
import { Component, ElementRef, OnInit, ViewChild, AfterViewInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { StoreFrontAPIConstant } from '../../constants/api.constants';
import { ArticleService } from '../../services/article.service';
import { debounceTime, Subject } from 'rxjs';
// import { OwlOptions } from 'ngx-owl-carousel-o';

@Component({
  selector: 'pmi-search-articles',
  templateUrl: './search-articles.component.html',
  styleUrls: ['./search-articles.component.scss']
})
export class SearchArticlesComponent implements OnInit, AfterViewInit {
  @ViewChild("searchText", { static: false }) searchText!: ElementRef;
  id: string = '';
  loading: boolean = false;
  // categoryBannerDetails: any = {};
  subSearchArticles: any[] = [];
  selectedSubCategory: string = '';
  searchSubject = new Subject<string>();
  queryParams: any = {};
  // navSpeed = 2000;
  loaded = false;
  constructor(
    private route: ActivatedRoute,
    private http: HttpClient,
    private articleService: ArticleService,
    private router: Router
  ) { }

  ngAfterViewInit(): void {
    this.searchText.nativeElement.value = this.queryParams.t;
  }

  // setNavSpeed(data: any) {
  //   this.navSpeed = data.Banner_Timer || this.navSpeed;
  //   this.customOptions = {
  //     ...this.customOptions,
  //     navSpeed: this.navSpeed,
  //     autoplayTimeout: this.navSpeed,
  //     autoplaySpeed: this.navSpeed,
  //   };
  // }

  ngOnInit(): void {
    // this.fetchBannerDetails();
    this.route.queryParams.subscribe((params: any) => {
      if (!this.loaded && params.p) {
        this.currentPage = parseInt(params.p);
      }
      if (params.t) {
        this.searchTerm = params.t;
        this.queryParams.t = params.t;
      }
      this.loaded = true;
      this.fetchArticles();
    });
    this.searchSubject.pipe(debounceTime(500)).subscribe(searchTerm => { this.searchTerm = searchTerm; this.fetchArticles(); });
  }

  // fetchBannerDetails() {
  //   const url = `${StoreFrontAPIConstant.Categories_Page}?populate=deep`;
  //   this.http.get(url).subscribe({
  //     next: (value: any) => {
  //       if (value?.data?.attributes) {
  //         this.setNavSpeed(value?.data?.attributes);
  //         if (value?.data?.attributes?.Banner_images?.data?.length) {
  //           this.categoryBannerDetails = value?.data?.attributes.Banner_images.data;
  //         }
  //       }
  //     },
  //   });
  // }

  articles: any[] = [];
  totalCount: number = 0;
  pageSizeOptions: number[] = [9, 18, 36, 72];
  pageSize: number = 9;
  currentPage: number = 0;
  searchTerm: string = '';
  sort = 'updatedAt:desc';
  loadingArticles = false;
  view = 'grid';

  onValueSearch(target: HTMLInputElement) {
    this.searchSubject.next(target.value);
  }

  fetchArticles() {
    this.loadingArticles = true;
    this.articleService.getArticles(this.currentPage, this.pageSize, this.searchTerm, this.sort, "", [], true)
      .subscribe(response => {
        this.articles = response.data;
        this.totalCount = response.meta.pagination.total;
        this.loadingArticles = false;
      });
  }

  onPageChange(event: any) {
    if (!event) return;
    this.router.navigate([], {
      relativeTo: this.route,
      queryParams: {
        p: this.currentPage === 0 ? null : this.currentPage,
      },
      queryParamsHandling: 'merge',
    });
    this.fetchArticles();
  }

  // customOptions: OwlOptions = {
  //   loop: true,
  //   autoplay: true,
  //   dots: false,
  //   nav: false,
  //   navSpeed: this.navSpeed,
  //   autoplayTimeout: this.navSpeed,
  //   autoplaySpeed: this.navSpeed,
  //   animateOut: 'fadeOut',
  //   navText: ["<img src='/assets/images/arrow_back.svg' />", "<img src='/assets/images/arrow_forward.svg' />"],
  //   responsive: {
  //     0: {
  //       items: 1,
  //     },
  //     600: {
  //       items: 1,
  //     },
  //     1000: {
  //       items: 1,
  //     },
  //   },
  // };

  openArticle(slug: string) {
    this.router.navigate([`../articles`, slug], { relativeTo: this.route });
  }
}
