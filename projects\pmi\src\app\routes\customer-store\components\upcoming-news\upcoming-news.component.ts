import { Component, Input } from '@angular/core';
import { StoreFrontAPIConstant } from '../../constants/api.constants';
import { OwlOptions } from 'ngx-owl-carousel-o';

@Component({
  selector: 'app-upcoming-news',
  templateUrl: './upcoming-news.component.html',
  styleUrls: ['./upcoming-news.component.scss']
})
export class UpcomingNewsComponent {

  @Input() data: any;

  imgPath: string = StoreFrontAPIConstant.IMG_URL;

  navSpeed = 2000;

  ngOnInit(): void {
    this.navSpeed = this.data.Banner_Timer || this.navSpeed;
    this.newsOptions = {
      ...this.newsOptions,
      navSpeed: this.navSpeed,
      autoplayTimeout: this.navSpeed,
      autoplaySpeed: this.navSpeed,
    };
  }

  newsOptions: OwlOptions = {
    loop: true,
    autoplay: true,
    dots: false,
    nav: false,
    navSpeed: this.navSpeed,
    autoplayTimeout: this.navSpeed,
    autoplaySpeed: this.navSpeed,
    animateOut: 'fadeOut',
    touchDrag: false,
    mouseDrag: false,
    responsive: {
      0: {
        items: 1,
      },
      600: {
        items: 1,
      },
      1000: {
        items: 1,
      },
    },
  };

}
