import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { NgbDropdownModule, NgbTooltipModule, } from "@ng-bootstrap/ng-bootstrap";

import { CustomerStoreRoutingModule } from "./customer-store-routing.module";
import { CustomerStoreComponent } from "./customer-store.component";
import { SharedModule } from "../../shared/shared.module";

@NgModule({
  declarations: [
    CustomerStoreComponent,
  ],
  imports: [
    CommonModule,
    SharedModule,
    CustomerStoreRoutingModule,
    NgbDropdownModule,
    NgbTooltipModule,
  ],
})
export class CustomerStoreModule { }
