/*------CAMPAIGN SEC------*/
.banner-sec {
    margin: 0;
    padding: 0;
    height: 240px;
    background-size: cover !important;

    .banner-body {
        margin: 0 auto;
        padding: 0 30px;
        max-width: 1500px;
        height: 100%;
        align-items: center;
        justify-content: center;
        gap: 16px;

        h1 {
            margin: 40px 0 0 0;
            padding: 0;
            position: relative;
            display: flex;
            font-family: var(--brand-font-family-secondary);
            font-weight: var(--brand-font-weight-bold);
            color: var(--brand-color-deep-nevy-blue);
        }

        .tab-buttons {
            margin: 20px 0 0 0;
            position: relative;
            gap: 0 10px;

            .tab-btn {
                margin: 0;
                padding: 0;
                display: flex;
                align-items: center;
                justify-content: center;
                width: 220px;
                height: 46px;
                font-family: var(--brand-font-family-secondary);
                font-weight: var(--brand-font-weight-bold);
                background: var(--brand-color-white);
                border-radius: 50px;
                cursor: pointer;
                border: none;

                &.selected {
                    color: var(--brand-color-white);
                    background: var(--brand-color-primary);
                }

                &:hover {
                    opacity: 0.8;
                }
            }
        }
    }

    &:before {
        position: absolute;
        content: '';
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background: #00000024;
    }
}

.bedcrumbs-sec {
    padding: 20px 0;
    background: var(--brand-color-main-background-secondary);

    .bedcrumbs {
        margin: 0 auto !important;
        max-width: 1500px;
        padding: 0 30px;
        font-family: var(--brand-font-family-secondary);
        font-weight: var(--brand-font-weight-medium);
        font-size: var(--papa-m-font-size-0-875);
        color: var(--papa-m-color-dark-secondary);
        line-height: 12px;
    }
}


/*------CAMPAIGN SEC------*/
.my-activation-sec {
    margin: 0;
    padding: 20px 0 40px 0;
    position: relative;
    background: var(--brand-color-main-background-secondary);
    min-height: 100vh;

    .my-activation-body {
        margin: 0 auto 40px auto;
        padding: 0 30px;
        max-width: 1500px;
        gap: 30px;

        h3 {
            margin: 0 0 15px 0;
            padding: 0;
            color: var(--brand-color-primary);
            font-weight: var(--brand-font-weight-bold);
        }
    }
}

.activation-filter {
    gap: 0 14px;
    width: 100%;
    justify-content: flex-end;

    .activation-filter-text {
        margin: 0;
        padding: 0;
        max-width: 40%;
        font-size: 16px;
        line-height: 22px;
        color: var(--brand-color-dark-600);
    }

    .activation-search-box {
        max-width: 340px;
        width: 100%;

        .form-control {
            padding: 0 14px 0 36px;
            height: 48px !important;
            background: var(--brand-color-white);
            border: 1px solid var(--brand-color-light-border) !important;
            border-radius: 8px;
            box-shadow: 0 6px 6px #00000008 !important;
            font-size: var(--brand-font-size-1);
        }

        label {
            margin: auto;
            top: 0;
            bottom: 0;
            left: 8px;
            height: 24px;
            width: 24px;
            color: var(--brand-color-dark-600);
        }
    }
}


.show-table-list {
    font-size: 16px;
    line-height: 22px;
    color: var(--brand-color-dark-600);
    gap: 0 8px;
}

.activation-table-sec {
    margin: 40px 0 0 0;
    padding: 16px;
    position: relative;
    background: var(--brand-color-white);
    border: 1px solid var(--brand-offwhite);
    border-radius: 12px;
    box-shadow: 0 2px 4px #0000000d;

    thead {
        position: sticky;
        top: 0;
        z-index: 10;

        th {
            border: none;
            background: var(--brand-border-color);
            height: 70px;
            vertical-align: middle;

            &:first-child {
                border-top-left-radius: 8px;
                border-bottom-left-radius: 8px;
            }

            &:last-child {
                border-top-right-radius: 8px;
                border-bottom-right-radius: 8px;
            }

            .table-th {
                position: relative;
                margin: 0;
                display: flex;
                align-items: center;
                justify-content: space-between;
                font-size: 16px;
                color: var(--brand-color-deep-nevy-blue);
                gap: 0 8px;
                cursor: pointer;

                span {
                    font-size: 20px;
                    font-weight: 600;
                    color: var(--brand-color-tertiary-gray);
                }
            }
        }
    }

    tbody {
        tr {
            td {
                border-top: none;
                border-bottom: 1px solid var(--brand-border-color);
                vertical-align: middle;
                height: 68px;

                .table-td {
                    position: relative;
                    margin: 0;
                    display: flex;
                    justify-content: start;
                    font-size: 15px;
                    color: var(--brand-color-dark-600);
                    gap: 0 8px;
                    cursor: pointer;

                    a {
                        color: var(--brand-color-secondary-accent);
                        text-decoration: underline !important;
                    }

                    .related-case {
                        color: #9fa2a9;
                    }

                    .info-btn {
                        margin: 0 0 0 auto;
                        padding: 0;
                        position: relative;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        gap: 0 6px;
                        width: 30px;
                        height: 30px;
                        color: var(--brand-color-deep-nevy-blue);
                        background: var(--whitecolor);
                        border: none;
                        border-radius: 100px;
                        cursor: pointer;

                        span {
                            color: var(--brand-color-primary);
                        }
                    }
                }
            }
        }
    }
}

.activation-table-body {
    padding: 0 16px 0 0;
    position: relative;
    overflow: auto;
    height: calc(100vh - 300px);
    margin: 0;
    padding: 0 16px 0 0;
    position: relative;
    overflow: auto;
    height: calc(100vh - 300px);

    &::-webkit-scrollbar {
        width: 7px;
        height: 5px;
        width: 7px;
        height: 5px;
    }

    &::-webkit-scrollbar-track {
        background: var(--brand-color-white);
        border: 1px solid var(--brand-color-light-border);
        border-radius: 50px;
        background: var(--brand-color-white);
        border: 1px solid var(--brand-color-light-border);
        border-radius: 50px;
    }

    &::-webkit-scrollbar-thumb {
        background: var(--brand-color-light-border);
        border-radius: 50px;
        background: var(--brand-color-light-border);
        border-radius: 50px;
    }

    thead {
        position: sticky;
        top: 0;
        z-index: 10;

        th {
            border: none;
            background: var(--brand-border-color);
            height: 70px;
            vertical-align: middle;

            &:first-child {
                border-top-left-radius: 8px;
                border-bottom-left-radius: 8px;
            }

            &:last-child {
                border-top-right-radius: 8px;
                border-bottom-right-radius: 8px;
            }

            .table-th {
                position: relative;
                margin: 0;
                display: flex;
                align-items: center;
                justify-content: space-between;
                font-size: 16px;
                color: var(--brand-color-deep-nevy-blue);
                gap: 0 8px;
                cursor: pointer;

                span {
                    font-size: 20px;
                    font-weight: 600;
                    color: var(--brand-color-tertiary-gray);
                }
            }
        }
    }

    tbody {
        tr {
            td {
                border-top: none;
                border-bottom: 1px solid var(--brand-border-color);
                vertical-align: middle;
                height: 68px;

                .table-td {
                    position: relative;
                    margin: 0;
                    display: flex;
                    justify-content: start;
                    color: var(--brand-color-dark-600);
                    gap: 0 8px;
                    cursor: pointer;

                    a {
                        color: var(--brand-color-secondary-accent);
                        text-decoration: underline !important;
                    }

                    .related-case {
                        color: #9fa2a9;
                    }

                    .info-btn {
                        margin: 0 0 0 auto;
                        padding: 0;
                        position: relative;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        gap: 0 6px;
                        width: 30px;
                        height: 30px;
                        color: #000;
                        background: var(--whitecolor);
                        border-radius: 100px;
                        cursor: pointer;

                        span {
                            color: var(--brand-color-primary);
                        }
                    }
                }
            }
        }
    }
}

.status {
    padding: 4px 8px;
    width: 96px;
    justify-content: center;
    gap: 0 4px;
    font-size: var(--brand-font-size-0-8);
    background: var(--brand-border-color);
    border-radius: 4px;
    font-size: var(--brand-font-size-0-8);
    padding: 4px 8px;
    background: var(--brand-border-color);
    width: 96px;
    justify-content: center;
    gap: 0 4px;
    border-radius: 4px;

    span {
        font-size: 20px;
        font-weight: 600;
        font-size: 20px;
        font-weight: 600;
    }

    &.closed {
        color: var(--brand-color-primary);
        background: #ff3a3a29;
        color: var(--brand-color-primary);
        background: #ff3a3a29;
    }

    &.open {
        color: var(--brand-color-secondary-accent);
        background: #05a4b024;
        color: var(--brand-color-secondary-accent);
        background: #05a4b024;
    }
}

.my-activation-header-sec {
    margin: 0;
    padding: 0;
    position: relative;
    background: var(--brand-color-white);
    border: 1px solid var(--brand-offwhite);
    border-radius: 12px;
    box-shadow: 0 2px 4px #0000000d;
    overflow: hidden;
}

.my-activation-header-top {
    margin: 0;
    padding: 16px;
    background: var(--brand-offwhite);

    .request {
        gap: 0 12px;

        .request-cnt {
            h5 {
                margin: 0;
                padding: 0;
                font-size: var(--brand-font-size-0-9);
                line-height: 18px;
                color: var(--brand-color-tertiary-dark-teal);
            }

            h3 {
                margin: 4px 0 0 0;
                padding: 0;
                font-size: var(--brand-font-size-26);
                line-height: 35px;
                color: var(--brand-color-deep-nevy-blue);
            }
        }
    }
}

.request {
    .request-img {
        margin: 0;
        padding: 0;
        position: relative;
        width: 64px;
        height: 64px;
        background: var(--brand-color-primary);
        border-radius: 100px;
        overflow: hidden;

        img {
            width: 33px;
        }
    }
}

.my-activation-header-btns {
    gap: 0 16px;

    .header-btn {
        margin: 0;
        padding: 0;
        justify-content: center;
        width: 150px;
        height: 48px;
        font-size: var(--brand-font-size-0-875);
        font-weight: var(--brand-font-weight-semi-bold);
        background: var(--brand-color-white);
        border-radius: 50px;
        cursor: pointer;
        gap: 0 8px;

        span {
            font-size: 22px;
            color: var(--brand-color-primary);
        }
    }
}

.my-activation-header-bottom {
    padding: 30px;
    position: relative;
    gap: 0 30px;
}

.my-activation-header-box {
    margin: 0;
    padding: 0;
    position: relative;
    flex: 1;
    gap: 8px;

    span {
        position: relative;
        width: 30px;
        height: 30px;
        align-items: center;
        justify-content: center;
        display: flex;
        font-size: 30px;
        font-weight: 500;
    }

    h4 {
        margin: 0;
        padding: 0;
        font-size: var(--brand-font-size-0-9);
        line-height: 18px;
        color: var(--brand-color-tertiary-dark-teal);
    }

    h5 {
        margin: 4px 0 0 0;
        padding: 0;
        font-size: var(--brand-font-size-1);
        line-height: 18px;
        color: var(--brand-color-deep-nevy-blue);
    }
}

.my-activation-form-sec {
    margin: 40px 0 0 0;
    padding: 0;
    position: relative;
    background: var(--brand-color-white);
    border: 1px solid var(--brand-offwhite);
    border-radius: 12px;
    box-shadow: 0 2px 4px #0000000d;
    overflow: hidden;
}

.my-activation-form-tab {
    margin: 0;
    padding: 16px;
    background: var(--brand-color-tertiary-beige);
    gap: 16px;

    .tab-btn {
        margin: 0;
        padding: 0;
        width: 150px;
        height: 48px;
        font-size: var(--brand-font-size-0-875);
        font-weight: var(--brand-font-weight-semi-bold);
        background: var(--brand-color-white);
        border-radius: 50px;
        cursor: pointer;
        gap: 0 8px;

        &.selected {
            color: var(--brand-color-white);
            background: var(--brand-color-primary);
        }
    }
}

.my-activation-form-body {
    margin: 0;
    padding: 30px;
    position: relative;

    form {
        gap: 30px;
        position: relative;
        justify-content: space-between;

        .form-group {
            margin: 0;
            padding: 0;
            position: relative;
            width: 48.5%;

            label {
                margin: 0 0 8px 0;
                padding: 0;
                display: flex;
                font-size: var(--brand-font-size-0-875);
                line-height: 18px;
                color: var(--brand-color-dark-600);
            }

            .form-control {
                margin: 0;
                padding: 12px;
                height: 48px;
                background: var(--brand-color-white);
                border-radius: 8px;
                border: 1px solid var(--brand-accordian-button-color) !important;
                font-size: var(--brand-font-size-0-9);
                box-shadow: none;
            }

            textarea {
                &.form-control {
                    height: 200px;
                }
            }

            .edit-form-btn {
                position: absolute;
                right: 12px;
                bottom: 12px;
                width: 22px;
                height: 22px;
                background: none;
                opacity: 0.6;
                cursor: pointer;
            }

            &.user-box {
                gap: 0 10px;

                .user-img {
                    margin: 0;
                    padding: 0;
                    position: relative;
                    width: 58px;
                    height: 58px;
                    background: var(--brand-color-primary);
                    border-radius: 100px;
                    overflow: hidden;

                    img {
                        width: 30px;
                    }
                }
            }
        }
    }
}

.full-width {
    width: 100% !important;
}

.check-box {
    margin: 25px 0 0 0;
    padding: 0 12px;
    position: relative;
    height: 48px;
    background: var(--brand-color-white);
    border-radius: 8px;
    border: 1px solid var(--brand-accordian-button-color) !important;
    font-family: var(--brand-font-family-secondary);
    font-size: var(--brand-font-size-0-9);
    font-weight: var(--brand-font-weight-medium);
    box-shadow: none;
    gap: 0 8px;

    input {
        min-width: 16px;
        height: 16px;
    }

    label {
        margin: 0 !important;
        gap: 0 5px;
        width: 100%;
        justify-content: space-between;
    }
}

.form-group {
    &.user-box {
        .user-cnt {
            h5 {
                margin: 0;
                padding: 0;
                font-size: var(--brand-font-size-0-9);
                line-height: 18px;
                color: var(--brand-grey);
                font-weight: var(--brand-font-weight-normal);
            }

            h3 {
                margin: 8px 0 0 0;
                padding: 0;
                font-size: var(--brand-font-size-1-25);
                line-height: 20px;
                color: var(--brand-color-deep-nevy-blue);

                span {
                    color: var(--brand-color-tertiary-dark-teal);
                }
            }
        }
    }
}

.related-file-upload-sec {
    margin: 0;
    padding: 30px;
    position: relative;
}

.related-file-upload-title {
    padding: 0 0 20px 0;
    border-bottom: 1px solid var(--brand-color-light-border);

    h3 {
        margin: 0;
        padding: 0;
        font-family: var(--brand-font-family-secondary);
        font-size: var(--brand-font-size-1-125);
        font-weight: var(--brand-font-weight-bold);
        line-height: 28px;
        color: var(--brand-color-deep-nevy-blue);
        gap: 0 8px;
    }

    span {
        color: var(--brand-color-secondary-accent);
    }

    .upload-btn {
        margin: 0;
        padding: 0;
        width: 192px;
        height: 48px;
        justify-content: center;
        font-family: var(--brand-font-family-secondary);
        font-weight: 700;
        color: var(--brand-color-deep-nevy-blue);
        background: var(--brand-color-white);
        border: 2px solid var(--brand-color-deep-nevy-blue);
        border-radius: 50px;
        cursor: pointer;
        gap: 0 8px;

        span {
            color: var(--brand-color-deep-nevy-blue);
        }
    }
}

.file-upload-form {
    margin: 20px 0 0 0;
    padding: 0;
    position: relative;

    .file-upload-label {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 200px;
        border: 1px dashed var(--brand-accordian-button-color);
        border-radius: 12px;
        box-shadow: 0 2px 4px #0000001a;
        cursor: pointer;

        .form-control-file {
            display: none;
        }

        .file-upload-btn {
            margin: 0;
            padding: 0;
            width: 192px;
            height: 48px;
            justify-content: center;
            font-family: var(--brand-font-family-secondary);
            font-weight: 700;
            color: var(--brand-color-primary);
            background: var(--brand-color-white);
            border: 2px solid var(--brand-color-primary);
            border-radius: 50px;
            cursor: pointer;
            gap: 0 8px;
        }
    }
}

.my-activation-submit-body {
    margin: 50px auto;
    padding: 0 30px;
    max-width: 1500px;
    gap: 0 20px;

    .submit-btn {
        margin: 0;
        padding: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 190px;
        height: 48px;
        font-family: var(--brand-font-family-secondary);
        font-weight: 700;
        background: var(--brand-color-primary);
        color: var(--brand-color-white);
        border-radius: 50px;
        cursor: pointer;
        gap: 0 6px;

        &.cancel {
            color: var(--brand-color-white);
            background: var(--brand-color-black);
        }
    }
}
.flex-grow-2 {
    flex-grow: 2;
}

.bedcrumbs-sec {
    padding: 20px 0;
    background: var(--brand-color-main-background-secondary);

    .bedcrumbs {
        margin: 0 auto !important;
        max-width: 1500px;
        padding: 0 30px;
        font-family: var(--brand-font-family-secondary);
        font-weight: var(--brand-font-weight-medium);
        font-size: var(--papa-m-font-size-0-875);
        color: var(--papa-m-color-dark-secondary);
        line-height: 12px;
    }
}