import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { BannerComponent } from './banner/banner.component';
import { ExtralinksComponent } from './extralinks/extralinks.component';
import { HomecategoryAndSdebarComponent } from './homecategory-and-sdebar/homecategory-and-sdebar.component';
import { PizzaworkComponent } from './pizzawork/pizzawork.component';
import { StoremanagerComponent } from './storemanager/storemanager.component';
import { FormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { DynamicComponentDirective } from '../directives/dynamic-component.directive';
import { CarouselModule } from 'ngx-owl-carousel-o';
import { UpcomingNewsComponent } from './upcoming-news/upcoming-news.component';
import { NgbDropdownModule } from '@ng-bootstrap/ng-bootstrap';

const components = [
  BannerComponent,
  UpcomingNewsComponent,
  HomecategoryAndSdebarComponent,
  ExtralinksComponent,
  PizzaworkComponent,
  StoremanagerComponent,
  DynamicComponentDirective,
]

@NgModule({
  declarations: [
    ...components,
    UpcomingNewsComponent
  ],
  imports: [
    CommonModule,
    FormsModule,
    RouterModule,
    CarouselModule,
    NgbDropdownModule
  ],
  exports: [
    FormsModule,
    ...components
  ]
})
export class StoreSharedModule { }
