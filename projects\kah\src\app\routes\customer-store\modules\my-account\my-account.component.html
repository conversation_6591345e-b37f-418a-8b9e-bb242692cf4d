<!-- accounts.component.html -->

<section class="profile-and-fav-banner-sec position-relative">
    <div class="profile-and-fav-banner-vody d-flex position-relative flex-column justify-content-center">
        <div class="profile-and-fav-title position-relative">My Profile</div>
        <div class="bedcrumbs d-flex align-items-center"> <span class="home-link cursor-pointer"
                routerLink="/store/home">Home</span>
            <span class="material-symbols-outlined">keyboard_arrow_right</span> My Profile
        </div>
    </div>
</section>

<section class="profile-and-fav-sec position-relative">
    <div class="profile-and-fav-body d-flex">
        <div class="pf-sidebar d-flex">
            <div class="pv-tab-list position-relative d-flex flex-column">
                <a href="javascript: void(0)" [class.active]="selectedSection == 'my-profile'"
                    (click)="selectedSection = 'my-profile'"><span class="material-symbols-outlined">person</span> My
                    Profile</a>
                <a [href]="adminPortalUrl" target="_blank" *ngIf="show.adaptUi"><span
                        class="material-symbols-outlined">hub</span> Adapt
                    UI</a>
                <a [routerLink]="['/backoffice']" *ngIf="show.backOffice"><span
                        class="material-symbols-outlined">desktop_landscape</span> Back
                    Office</a>
                <a href="javascript: void(0)" [class.active]="selectedSection == 'register-user'"
                    (click)="selectedSection = 'register-user'" *ngIf="show.register"><span
                        class="material-symbols-outlined">person_add</span>
                    Register a User</a>
                <a href="javascript: void(0)" [class.active]="selectedSection == 'account-overview'"
                    (click)="selectedSection = 'account-overview'" *ngIf="show.overview"><span
                        class="material-symbols-outlined">manage_accounts</span>
                    Account Overview</a>
            </div>
        </div>
        <div class="pf-body position-relative d-flex flex-column" *ngIf="selectedSection == 'my-profile'">
            <div class="d-flex align-items-center justify-content-center h-100" *ngIf="loadingUserDetails">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
            </div>
            <ng-container *ngIf="!loadingUserDetails">
                <div class="pf-top-sec position-relative d-flex align-items-center justify-content-between">
                    <div class="profile-img-sec position-relative d-flex align-items-center">
                        <!-- <div class="profile-img position-relative d-flex align-items-center justify-content-center">
                        <img src="images/profile-image.jpg" alt="" />
                    </div> -->
                        <div class="profile-cnt d-flex align-items-center">
                            <div class="profile-title position-relative">{{ userDetails.first_name + ' ' +
                                userDetails.last_name }}</div>
                        </div>
                    </div>
                    <div class="pf-logout-btn position-relative d-flex align-items-center justify-content-center">
                        <button type="button" class=" d-flex align-items-center justify-content-center"
                            (click)="logout()">
                            <span class="material-symbols-outlined">logout</span> Logout
                        </button>
                    </div>
                </div>
                <div class="pf-line position-relative d-flex"></div>
                <div class="profile-content-sec position-relative d-flex flex-column" *ngIf="profileForm">
                    <form [formGroup]="profileForm" (ngSubmit)="saveChanges()" autocomplete="off"
                        class="m-0 p-0 position-relative d-flex justify-content-between">
                        <div class="form-group">
                            <label>First Name</label>
                            <p class="val" *ngIf="!profileEditable">{{ f['firstName'].value || '-'}}</p>
                            <ng-container *ngIf="profileEditable">
                                <input type="text" class="form-control" formControlName="firstName"
                                    placeholder="Enter first name"
                                    [ngClass]="{ 'is-invalid': isFormSubmitted && f['firstName'].errors }">
                                <div *ngIf="isFormSubmitted && f['firstName'].errors" class="invalid-feedback">
                                    <div *ngIf="f['firstName'].errors['required']">
                                        This field is required</div>
                                </div>
                            </ng-container>
                        </div>
                        <div class="form-group">
                            <label>Last Name</label>
                            <p class="val" *ngIf="!profileEditable">{{ f['lastName'].value || '-'}}</p>
                            <ng-container *ngIf="profileEditable">
                                <input type="text" class="form-control" formControlName="lastName"
                                    placeholder="Enter last name"
                                    [ngClass]="{ 'is-invalid': isFormSubmitted && f['lastName'].errors }">
                                <div *ngIf="isFormSubmitted && f['lastName'].errors" class="invalid-feedback">
                                    <div *ngIf="f['lastName'].errors['required']">
                                        This field is required</div>
                                </div>
                            </ng-container>
                        </div>
                        <div class="form-group">
                            <label>User Name</label>
                            <p class="val" *ngIf="!profileEditable">{{ f['userName'].value || '-'}}</p>
                            <ng-container *ngIf="profileEditable">
                                <input type="text" class="form-control" formControlName="userName" placeholder="<EMAIL>">
                            </ng-container>
                        </div>
                        <div class="form-group">
                            <label>Communications Email</label>
                            <p class="val" *ngIf="!profileEditable">{{ f['email'].value || '-'}}</p>
                            <ng-container *ngIf="profileEditable">
                                <input type="text" class="form-control" formControlName="email"
                                    placeholder="Enter email"
                                    [ngClass]="{ 'is-invalid': isFormSubmitted && f['email'].errors }">
                                <div *ngIf="isFormSubmitted && f['email'].errors" class="invalid-feedback">
                                    <div *ngIf="f['email'].errors['required']">
                                        This field is required</div>
                                    <div *ngIf="f['email'].errors['email']">
                                        Please enter a valid email address.</div>
                                </div>
                            </ng-container>
                        </div>
                        <div class="form-group full-width">
                            <label>Address 1</label>
                            <p class="val" *ngIf="!profileEditable">{{ f['address'].value || '-'}}</p>
                            <ng-container *ngIf="profileEditable">
                                <input type="text" class="form-control" formControlName="address" placeholder="Address">
                            </ng-container>
                        </div>
                        <div class="form-group">
                            <label>City</label>
                            <p class="val" *ngIf="!profileEditable">{{ f['city'].value || '-'}}</p>
                            <ng-container *ngIf="profileEditable">
                                <input type="text" class="form-control" formControlName="city" placeholder="City">
                            </ng-container>
                        </div>

                        <div class="form-group">
                            <label>Country</label>
                            <p class="val" *ngIf="!profileEditable">{{ f['country'].value || '-'}}</p>
                            <ng-container *ngIf="profileEditable">
                                <select class="form-control" formControlName="country">
                                    <option selected hidden disabled>Choose</option>
                                    <option value="USA">USA</option>
                                    <option value="Canada">Canada</option>
                                </select>
                            </ng-container>
                        </div>
                        <div class="form-group">
                            <label>ZIP</label>
                            <p class="val" *ngIf="!profileEditable">{{ f['zipCode'].value || '-'}}</p>
                            <ng-container *ngIf="profileEditable">
                                <input type="text" class="form-control" formControlName="zipCode" />
                            </ng-container>
                        </div>
                        <div class="form-group">
                            <label>State</label>
                            <p class="val" *ngIf="!profileEditable">{{ f['state'].value || '-'}}</p>
                            <ng-container *ngIf="profileEditable">
                                <select class="form-control" formControlName="state">
                                    <option selected hidden disabled>Choose</option>
                                    <ng-container *ngIf="f['country'].value == 'USA'">
                                        <option *ngFor="let city of usStates" [value]="city.key">{{ city.value }}
                                        </option>
                                    </ng-container>
                                    <ng-container *ngIf="f['country'].value == 'Canada'">
                                        <option *ngFor="let city of canadaProvincesAndTerritories" [value]="city.key">{{
                                            city.value }}</option>
                                    </ng-container>
                                </select>
                            </ng-container>
                        </div>
                        <div class="form-group">
                            <label>Direct Phone</label>
                            <p class="val" *ngIf="!profileEditable">{{ f['directPhone'].value || '-'}}</p>
                            <ng-container *ngIf="profileEditable">
                                <input type="text" class="form-control" formControlName="directPhone"
                                    placeholder="Direct Phone"
                                    [ngClass]="{ 'is-invalid': isFormSubmitted && f['directPhone'].errors }">
                                <div *ngIf="isFormSubmitted && f['directPhone'].errors" class="invalid-feedback">
                                    <div *ngIf="f['directPhone'].errors['pattern']">
                                        Please enter a valid phone
                                        number.</div>
                                </div>
                            </ng-container>
                        </div>
                        <div class="form-group">
                            <label>Office Phone</label>
                            <p class="val" *ngIf="!profileEditable">{{ f['officePhone'].value || '-'}}</p>
                            <ng-container *ngIf="profileEditable">
                                <input type="text" class="form-control" formControlName="officePhone"
                                    placeholder="Office Phone"
                                    [ngClass]="{ 'is-invalid': isFormSubmitted && f['officePhone'].errors }">
                                <div *ngIf="isFormSubmitted && f['officePhone'].errors" class="invalid-feedback">
                                    <div *ngIf="f['officePhone'].errors['pattern']">
                                        Please enter a valid phone
                                        number.</div>
                                </div>
                            </ng-container>
                        </div>
                        <div class="form-group">
                            <label>Cell Phone</label>
                            <p class="val" *ngIf="!profileEditable">{{ f['cellPhone'].value || '-'}}</p>
                            <ng-container *ngIf="profileEditable">
                                <input type="text" class="form-control" formControlName="cellPhone"
                                    placeholder="Cell Phone"
                                    [ngClass]="{ 'is-invalid': isFormSubmitted && f['cellPhone'].errors }">
                                <div *ngIf="isFormSubmitted && f['cellPhone'].errors" class="invalid-feedback">
                                    <div *ngIf="f['cellPhone'].errors['pattern']">
                                        Please enter a valid phone
                                        number.</div>
                                </div>
                            </ng-container>
                        </div>
                        <div class="form-group">
                            <label>Other Phone</label>
                            <p class="val" *ngIf="!profileEditable">{{ f['otherPhone'].value || '-'}}</p>
                            <ng-container *ngIf="profileEditable">
                                <input type="text" class="form-control" formControlName="otherPhone"
                                    placeholder="Other Phone"
                                    [ngClass]="{ 'is-invalid': isFormSubmitted && f['otherPhone'].errors }">
                                <div *ngIf="isFormSubmitted && f['otherPhone'].errors" class="invalid-feedback">
                                    <div *ngIf="f['otherPhone'].errors['pattern']">
                                        Please enter a valid phone
                                        number.</div>
                                </div>
                            </ng-container>
                        </div>
                        <div class="form-group">
                            <label>Fax</label>
                            <p class="val" *ngIf="!profileEditable">{{ f['fax'].value || '-'}}</p>
                            <ng-container *ngIf="profileEditable">
                                <input type="text" class="form-control" formControlName="fax" placeholder="Fax" />
                            </ng-container>
                        </div>
                        <div class="form-group">
                            <label>Date of Birth</label>
                            <p class="val" *ngIf="!profileEditable">{{ f['dateOfBirth'].value || '-'}}</p>
                            <ng-container *ngIf="profileEditable">
                                <input type="date" class="form-control" formControlName="dateOfBirth"
                                    placeholder="Enter email">
                            </ng-container>
                        </div>
                        <div class="form-group">
                            <label>Gender</label>
                            <p class="val" *ngIf="!profileEditable">{{ f['gender'].value || '-'}}</p>
                            <ng-container *ngIf="profileEditable">
                                <select class="form-control" formControlName="gender">
                                    <option selected hidden disabled>Choose</option>
                                    <option>Male</option>
                                    <option>Female</option>
                                </select>
                            </ng-container>
                        </div>
                        <ng-container *ngIf="profileEditable">
                            <form [formGroup]="changePasswordForm" autocomplete="off" class="m-0 p-0 position-relative d-flex justify-content-between">
                                <div class="pf-line"></div>
                                <div class="form-group">
                                    <label>Current Password</label>
                                    <input type="password" formControlName="currentPassword"
                                        class="form-control mt-1 mb-2" autocomplete="new-password"
                                        [ngClass]="{ 'is-invalid': isPasswordFormSubmitted && cf['currentPassword'].errors }" />
                                    <div *ngIf="isPasswordFormSubmitted && cf['currentPassword'].errors"
                                        class="invalid-feedback">
                                        <div *ngIf="cf['currentPassword'].errors['required']">
                                            This field is required</div>
                                        <div *ngIf="cf['currentPassword'].errors['minlength']">
                                            Must be at least 8 characters</div>
                                        <div *ngIf="cf['currentPassword'].errors['hasNumber']">
                                            Must contain at least one number</div>
                                        <div *ngIf="cf['currentPassword'].errors['hasCapitalCase']">
                                            Must contain at least one Letter in Capital Case</div>
                                        <div *ngIf="cf['currentPassword'].errors['hasSmallCase']">
                                            Must contain at least one Letter in Small Case</div>
                                        <div *ngIf="cf['currentPassword'].errors['hasSpecialCharacters']">
                                            Must contain at least one Special Character</div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label>Re-enter Password</label>
                                    <input type="password" formControlName="confirmPassword"
                                        class="form-control mt-1 mb-2"
                                        [ngClass]="{ 'is-invalid': isPasswordFormSubmitted && cf['confirmPassword'].errors }" />
                                    <div *ngIf="isPasswordFormSubmitted && cf['confirmPassword'].errors"
                                        class="invalid-feedback">
                                        <div *ngIf="cf['confirmPassword'].errors['required']">
                                            This field is required</div>
                                        <div *ngIf="cf['confirmPassword'].errors['confirmedValidator']">
                                            Passwords must match
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <a href="javascript:void(0)" class="change-password position-relative"
                                        (click)="changePassword()">Change
                                        Password</a>
                                </div>
                            </form>
                            <div class="pf-line"></div>
                            <div class="save-btn-sec p-0 d-flex align-items-center">
                                <button type="submit" [disabled]="saving"
                                    class="m-0 position-relative d-flex align-items-center justify-content-center">
                                    <span class="material-symbols-outlined">save</span>
                                    {{saving ? 'Saving' : 'Save Changes'}}
                                </button>
                                <button type="button"
                                    class="cancel-btn m-0 p-0 position-relative d-flex align-items-center justify-content-center"
                                    (click)="cancelChanges()"><span class="material-symbols-outlined">cancel</span>
                                    Cancel</button>
                            </div>
                        </ng-container>
                    </form>
                </div>
            </ng-container>
        </div>

        <div class="pf-body position-relative d-flex flex-column"
            *ngIf="selectedSection == 'register-user' && show.register">
            <div class="d-flex align-items-center justify-content-center h-100" *ngIf="loadingbpusers">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
            </div>
            <ng-container *ngIf="!loadingbpusers">
                <div class="profile-content-sec position-relative d-flex flex-column" *ngIf="registerForm">
                    <form [formGroup]="registerForm" (ngSubmit)="registerUser()" autocomplete="off"
                        class="m-0 p-0 position-relative d-flex justify-content-between">
                        <div class="form-group">
                            <label>Customer ID</label>
                            <ng-select [items]="customers$ | async" bindLabel="bp_id" class="custom"
                                *ngIf="userRoleType !== 'STOREFRONT'" [multiple]="false" [hideSelected]="true"
                                [loading]="customerLoading" [minTermLength]="0" [typeahead]="customerInput$"
                                formControlName="customers" (add)="onAddCustOption($event)" [maxSelectedItems]="10"
                                [placeholder]="'Select \'ALL\' or enter 2 or more chars to search customer'"
                                typeToSearchText="Enter 2 or more chars to search customer">
                                <ng-template ng-option-tmp let-item="item">
                                    <span>{{ item.bp_id }}</span>
                                    <span *ngIf="item.bp_full_name">: {{ item.bp_full_name }}</span>
                                </ng-template>
                            </ng-select>
                            <select class="form-control" formControlName="customers"
                                *ngIf="userRoleType === 'STOREFRONT'">
                                <option *ngFor="let user of bpcustomers" [value]="user.bp_id">{{user.bp_full_name}}
                                </option>
                            </select>
                            <div *ngIf="isRegisterFormSubmitted && rf['customers'].errors"
                                class="invalid-feedback d-block">
                                <div *ngIf="rf['customers'].errors['required']">Customer ID is
                                    required
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label>Function</label>
                            <select formControlName="function" class="form-control"
                                [ngClass]="{ 'is-invalid': isRegisterFormSubmitted && rf['function'].errors }">
                                <option *ngFor="let function of functions" [value]="function.code">{{function.text}}
                                </option>
                            </select>
                            <div *ngIf="isRegisterFormSubmitted && rf['function'].errors"
                                class="invalid-feedback d-block">
                                <div *ngIf="rf['function'].errors['required']">Function is
                                    required
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label>First Name</label>
                            <input type="text" formControlName="firstName" class="form-control mt-1"
                                [ngClass]="{ 'is-invalid': isRegisterFormSubmitted && rf['firstName'].errors }" />
                            <div *ngIf="isRegisterFormSubmitted && rf['firstName'].errors" class="invalid-feedback">
                                <div *ngIf="rf['firstName'].errors['required']">First
                                    Name is
                                    required</div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label>Last Name</label>
                            <input type="text" formControlName="lastName" class="form-control mt-1"
                                [ngClass]="{ 'is-invalid': isRegisterFormSubmitted && rf['lastName'].errors }" />
                            <div *ngIf="isRegisterFormSubmitted && rf['lastName'].errors" class="invalid-feedback">
                                <div *ngIf="rf['lastName'].errors['required']">Last Name is required</div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label>Email Address</label>
                            <input type="text" formControlName="email" class="form-control mt-1"
                                [ngClass]="{ 'is-invalid': isRegisterFormSubmitted && rf['email'].errors }" />
                            <div *ngIf="isRegisterFormSubmitted && rf['email'].errors" class="invalid-feedback">
                                <div *ngIf="rf['email'].errors['required']">Email is required</div>
                                <div *ngIf="rf['email'].errors['email']">Email is invalid</div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label>Username</label>
                            <input type="text" formControlName="username" class="form-control mt-1" />
                        </div>
                        <div class="form-group">
                            <label>Phone #</label>
                            <input type="text" formControlName="phone" class="form-control mt-1"
                                [ngClass]="{ 'is-invalid': isRegisterFormSubmitted && rf['phone'].errors }" />
                            <div *ngIf="isRegisterFormSubmitted && rf['phone'].errors" class="invalid-feedback">
                                <div *ngIf="rf['phone'].errors['pattern']">Please enter a valid phone
                                    number.</div>
                            </div>
                        </div>
                        <div class="form-group mb-3 form-check w-100 d-block">
                            <input type="checkbox" formControlName="acceptTerms" class="form-check-input"
                                [ngClass]="{ 'is-invalid': isRegisterFormSubmitted && rf['acceptTerms'].errors }" />
                            <label for="acceptTerms" class="form-check-label">By creating an account you are agreeing to
                                the
                                Privacy
                                Policy and Tearms and Conditions.</label>
                        </div>
                        <div class="pf-line"></div>
                        <div class="save-btn-sec p-0 d-flex align-items-center">
                            <button type="submit" [disabled]="registering"
                                class="m-0 position-relative d-flex align-items-center justify-content-center">
                                <span class="material-symbols-outlined">person_add</span>
                                {{registering ? 'Registering' : 'Register'}}
                            </button>
                            <button type="button"
                                class="cancel-btn m-0 p-0 position-relative d-flex align-items-center justify-content-center"
                                (click)="cancelRegisterChanges()"><span class="material-symbols-outlined">cancel</span>
                                Cancel</button>
                        </div>
                    </form>
                </div>
            </ng-container>
        </div>

        <div class="pf-body position-relative d-flex flex-column"
            *ngIf="selectedSection == 'account-overview' && show.overview">
            <ng-container *ngIf="!loadingbpusers">
                <div class="acc-overview-title">Account Overview</div>
                <div class="acc-details-box" *ngFor="let user of bpcustomers">
                    <div class="acc-details-title">
                        <span>Account Details</span>
                        <button type="button" class="acc-details-box-arrow"
                            (click)="user.acc_overview_btn = !user.acc_overview_btn"
                            [ngClass]="user.acc_overview_btn ? 'open' : 'close'">
                            <span class="material-symbols-outlined">keyboard_arrow_down</span>
                        </button>
                    </div>
                    <div class="acc-details-lists">
                        <div class="action-container">
                            <button type="button" class="edit-form-btn" (click)="user.editing = true"
                                *ngIf="!user.editing">
                                <span class="material-symbols-outlined">edit</span>
                            </button>
                            <button type="button" class="edit-form-btn" [disabled]="user.saving"
                                (click)="user.saving = true;saveAccount(user);" *ngIf="user.editing">
                                <span class="material-symbols-outlined">save</span>
                            </button>
                            <button type="button" class="edit-form-btn" (click)="user.editing = false"
                                *ngIf="user.editing">
                                <span class="material-symbols-outlined">close</span>
                            </button>
                        </div>
                        <div class="acc-details-text">
                            <span>#ID:</span>
                            <span class="val">{{ user.bp_id }}</span>
                        </div>
                        <div class="acc-details-text">
                            <span>Name:</span><span class="val"> {{ user.bp_full_name }}</span>
                        </div>
                        <div class="acc-details-text">
                            <span>Email:</span>
                            <span class="val" *ngIf="!user.editing">{{ user.email }}</span>
                            <div class="edit-field" *ngIf="user.editing">
                                <input type="text" class="edit-form" [(ngModel)]="user.email">
                            </div>
                        </div>
                        <div class="acc-details-text">
                            <span>Phone:</span>
                            <span class="val" *ngIf="!user.editing">{{ user.phone }}</span>
                            <div class="edit-field" *ngIf="user.editing">
                                <input type="text" class="edit-form" [(ngModel)]="user.phone">
                            </div>
                        </div>
                        <div class="acc-details-text" *ngFor="let address of user.bp_addresses">
                            <span>Address:</span>
                            <span class="val"> {{ address.street_name + ', ' + address.city_name + ', ' +
                                address.country + ', ' + address.postal_code }}
                            </span>
                        </div>
                    </div>
                    <div class="acc-details-table" [ngClass]="user.acc_overview_btn ? 'show' : 'hide'">
                        <div class="acc-details-table-title">Associated Contacts</div>
                        <div class="table table-responsive">
                            <table>
                                <thead>
                                    <tr>
                                        <th>Contact ID</th>
                                        <th>Name</th>
                                        <th>Email</th>
                                        <th>Username</th>
                                        <th>Function</th>
                                        <th>Phone</th>
                                        <th>Password</th>
                                        <th>Status</th>
                                        <th></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr *ngFor="let customer of user.bp_contacts_map">
                                        <td>{{ customer.contact_id }}</td>
                                        <td>{{ customer.contact?.first_name + (customer.contact?.middle_name ? (' ' +
                                            customer.contact?.middle_name) : '') + ' '
                                            + customer.contact?.last_name }}</td>
                                        <td>{{ customer.contact?.email }}</td>
                                        <td>{{ customer.contact?.portal_user_name }}</td>
                                        <ng-container *ngIf="!customer.editing">
                                            <td>{{ functionsObj[customer.contact?.function_code] }}</td>
                                            <td>{{ customer.contact?.phone }}</td>
                                        </ng-container>
                                        <ng-container *ngIf="customer.editing">
                                            <td>
                                                <div class="edit-field">
                                                    <select [(ngModel)]="customer.contact.function_code">
                                                        <option *ngFor="let function of functions"
                                                            [value]="function.code">{{function.text}}
                                                        </option>
                                                    </select>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="edit-field">
                                                    <input type="text" class="edit-form"
                                                        [(ngModel)]="customer.contact.phone">
                                                </div>
                                            </td>
                                        </ng-container>
                                        <td><button type="button" class="reset-p"
                                                (click)="resetPassword(customer)">Reset</button></td>
                                        <td>
                                            <label for="status" class="status-label" *ngIf="customer.editing">
                                                <input type="checkbox" id="status"
                                                    [(ngModel)]="customer.contact.is_portal_user" />
                                                <span class="active-s">Active</span>
                                                <span class="inactive-s">Inactive</span>
                                            </label>
                                            <label *ngIf="!customer.editing">{{ customer.contact.is_portal_user ?
                                                'Active' : 'Inactive' }}</label>
                                        </td>
                                        <td>
                                            <div class="d-flex gap-2">
                                                <button type="button" class="edit-table-btn"
                                                    [disabled]="customer.saving"
                                                    (click)="customer.saving = true;saveContact(customer);"
                                                    *ngIf="customer.editing">
                                                    <span class="material-symbols-outlined">save</span>
                                                </button>
                                                <button type="button" class="edit-table-btn"
                                                    (click)="customer.editing = false" *ngIf="customer.editing">
                                                    <span class="material-symbols-outlined">close</span>
                                                </button>
                                            </div>
                                            <button type="button" class="edit-table-btn"
                                                (click)="customer.editing = true" *ngIf="!customer.editing">
                                                <span class="material-symbols-outlined">edit</span>
                                            </button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </ng-container>
        </div>


    </div>
</section>