import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

import { SessionRoutingModule } from './session-routing.module';
import { ResetPasswordService } from './reset-password/reset-password.service';
import { ResetPasswordComponent } from './reset-password/reset-password.component';
import { LoginComponent } from './login/login.component';
import { ForgotPasswordComponent } from './forgot-password/forgot-password.component';
import { ForgotPasswordService } from './forgot-password/forgot-password.service';
import { ApiConstantsService, AuthService } from 'ng-snjya';
import { CarouselModule } from 'ngx-owl-carousel-o';
import { DefaultPasswordChangeComponent } from './default-password-change/default-password-change.component';

@NgModule({
  declarations: [
    LoginComponent,
    ResetPasswordComponent,
    ForgotPasswordComponent,
    DefaultPasswordChangeComponent,
  ],
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    SessionRoutingModule,
    CarouselModule
  ],
  providers: [
    ApiConstantsService,
    AuthService,
    ResetPasswordService,
    ForgotPasswordService,
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class SessionModule { }
