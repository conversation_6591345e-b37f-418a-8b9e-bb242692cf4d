<section class="upcoming-news-sec">
    <div class="upcoming-news-body">
        <div class="up-news-title"><img [src]="[data?.Upcomming_msg_icon.data.attributes.url]" alt="" />
            <span>{{data?.Upcoming_msg_title}}</span>
        </div>
        <div class="upcoming-news-slide">
            <owl-carousel-o [options]="newsOptions">
                <ng-container *ngFor="let upcomingMsgData of data?.upcoming_msg_list">
                    <ng-template carouselSlide [id]="upcomingMsgData.id.toString()">
                        <div class="upcoming-msg-list d-flex position-relative">
                            <div class="up-news-desc">{{upcomingMsgData?.upcomimg_msg_desc}}</div>
                            <div class="upcoming-n-img"
                                style="background: url('{{upcomingMsgData?.upcomimg_msg_img.data.attributes.url}}') center center no-repeat;">
                            </div>
                        </div>
                    </ng-template>
                </ng-container>
            </owl-carousel-o>
        </div>
    </div>
</section>