import { Component, OnInit } from '@angular/core';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { CapmaignCenterService } from '../../modules/campaign-center/campaign-center.service';
import { AuthService, ToastService } from 'ng-snjya';

@Component({
    selector: 'pmi-ask-permission',
    templateUrl: './ask-permission.component.html',
    styleUrls: ['./ask-permission.component.scss'],
})
export class AskPermissionComponent implements OnInit {

    saving = false;
    userDetails!: any;

    constructor(
        public activeModal: NgbActiveModal,
        private service: CapmaignCenterService,
        private authService: AuthService,
        private _snackBar: ToastService,
    ) { }

    ngOnInit(): void {
        this.userDetails = this.authService.userDetail;
    }

    submit() {
        this.saving = true;
        const obj = {
            "team": "PCM",
            "assignedTo": "",
            "customerId": "",
            "contactId": "",
            "subType": "188",
            "sub_subType": "",
            "msgType": "",
            "selectedTextMsg": "",
            "sendDate": "",
            "sendTime": "",
            "useLocalTZ": "",
            "offer_endDate": "",
            "targetMarkets": "",
            "customMessage": "",
            "askPermission": "Yes",
            "requestor": this.userDetails?.display_name || '',
            "email": this.userDetails?.email || '',
        };
        if (this.userDetails?.business_partners?.length) {
            const userDetails = this.userDetails?.business_partners[0];
            if (userDetails) {
                obj.customerId = userDetails.bp_id;
                if (userDetails.bp_contacts_map?.length) {
                    obj.contactId = userDetails.bp_contacts_map[0].contact_id;
                }
            }
        }
        this.service.createMarketingTicket(obj).subscribe(data => {
            this.saving = false;
            this._snackBar.open('Request sent successfully!');
            this.activeModal.close();
        }, () => {
            this.saving = false;
            this._snackBar.open('Error while processing your request.', {
                type: 'Error',
            });
        });
    }
}
