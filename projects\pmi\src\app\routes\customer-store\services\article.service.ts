import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { StoreFrontAPIConstant } from '../constants/api.constants';
import { lastValueFrom, map, Observable } from 'rxjs';
@Injectable({
    providedIn: 'root'
})
export class ArticleService {

    constructor(private http: HttpClient) {
    }

    async getSignedUrl(Key: string) {
        const res = await lastValueFrom(this.http.get<any>(`${StoreFrontAPIConstant.GetSignedUrl}/${Key}`).pipe(
            map((res) => res.url)
        ));
        return res;
    }

    getArticles(page: number, pageSize: number, searchTerm: string, sort: string, category: string, subCategories: string[], subCategorySelected: boolean): Observable<any> {
        let params = new HttpParams()
            .set('populate', 'deep,2')
            .set('sort', sort)
            .set('pagination[page]', page)
            .set('pagination[pageSize]', pageSize.toString());
        params = params.set('filters[$and][0][Type][$eq]', 'External')

        if (!subCategorySelected) {
            params = params.set('filters[$and][1][$or][0][categories][$eq]', category)

        }
        if (subCategories.length) {
            for (let i = 0; i < subCategories.length; i++) {
                params = params.set(`filters[$and][1][$or][${i + 1}][sub_categories][$in]`, subCategories[i]);
            }
        }

        if (searchTerm) {
            params = params.set(`filters[$and][2][$or][0][Heading][$containsi]`, searchTerm);
            params = params.set(`filters[$and][2][$or][1][Keywords][$containsi]`, searchTerm);
            params = params.set(`filters[$and][2][$or][2][Body][$containsi]`, searchTerm);
        }

        const url = `${StoreFrontAPIConstant.Articles}`;
        return this.http.get<any[]>(url, { params });
    }

    searchArticles(searchTerm: string, selectedCategory: any) {
        let params = new HttpParams();

        if (!selectedCategory?.subCategoryId) {
            params = params.set('filters[$and][0][categories][$eq]', selectedCategory.categoryId)
        } else {
            params = params.set(`filters[$and][0][sub_categories][$eq]`, selectedCategory?.subCategoryId);
        }

        if (searchTerm) {
            params = params.set(`filters[$and][1][$or][0][Heading][$containsi]`, searchTerm);
            params = params.set(`filters[$and][1][$or][1][Keywords][$containsi]`, searchTerm);
        }
        return this.http.get<any>(
            `${StoreFrontAPIConstant.Articles}?fields[0]=Heading`,
            { params }
        ).pipe(
            map((response) => {
                return response.data.map((data: any) => ({ id: data.id, name: data.attributes.Heading }))
            })
        );
    }

}
