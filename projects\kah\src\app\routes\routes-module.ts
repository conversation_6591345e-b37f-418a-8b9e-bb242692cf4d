import { NgModule } from "@angular/core";
import { Injectable } from "@angular/core";
import { Routes, RouterModule, Resolve } from "@angular/router";
import {
  AuthFeaturesGuard,
  AuthGuard,
  AuthSelectCustomerGuard,
  BackofficeLayoutComponent,
  CartService,
} from "ng-snjya";
import { CustomerStoreComponent } from "./customer-store/customer-store.component";

@Injectable({
  providedIn: "root",
})
export class RouteResolver implements Resolve<any> {
  //inject the api service we initially implemented
  constructor(private cartService: CartService) {}

  //resolve cart data
  async resolve() {
    return await this.cartService.getCartByID();
  }
}

const routes: Routes = [
  {
    path: "backoffice",
    component: BackofficeLayoutComponent,
    canActivate: [AuthGuard],
    canActivateChild: [AuthGuard],
    children: [
      { path: "", redirectTo: "dashboard", pathMatch: "full" },
      {
        path: "dashboard",
        loadChildren: () => import("ng-snjya").then((m) => m.DashboardModule),
      },
      {
        path: "data",
        loadChildren: () =>
          import("./backoffice/backoffice.module").then(
            (m) => m.BackofficeModule
          ),
      },
    ],
    data: { permission: "P0027" },
  },
  {
    path: "store",
    canActivate: [AuthGuard],
    canActivateChild: [AuthFeaturesGuard, AuthGuard],
    component: CustomerStoreComponent,
    loadChildren: () =>
      import("./customer-store/customer-store.module").then(
        (m) => m.CustomerStoreModule
      ),
    resolve: {
      routeResolver: RouteResolver,
    },
    data: { permission: "P0001" },
  },
  {
    path: "auth",
    loadChildren: () => import("./session/session.module").then((mod) => mod.SessionModule),
  },
  { path: "terms-of-use", loadChildren: () => import("./customer-store/modules/tearms-and-conditions/tearms-and-conditions.module").then((mod) => mod.TearmsAndConditionsModule), },
  { path: "privacy-policy", loadChildren: () => import("./customer-store/modules/privacy-policy/privacy-policy.module").then((mod) => mod.PrivacyPolicyModule), },
  { path: "", pathMatch: "full", redirectTo: "backoffice" },
];

@NgModule({
  imports: [
    RouterModule.forRoot(routes, {
      useHash: true,
    }),
  ],
  exports: [RouterModule],
})
export class RoutesRoutingModule {}
