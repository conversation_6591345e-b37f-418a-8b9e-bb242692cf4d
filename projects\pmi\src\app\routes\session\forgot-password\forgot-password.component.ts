import { Component, Input } from '@angular/core';
import {
  AbstractControl,
  FormBuilder,
  FormGroup,
  Validators,
} from '@angular/forms';

import { ForgotPasswordService } from './forgot-password.service';
import { ToastService } from 'ng-snjya';
import { StoreFrontAPIConstant } from '../../customer-store/constants/api.constants';
import { ComponentNameConstants } from '../../customer-store/constants/components.contants';
import { HomeService } from '../../customer-store/services/home.service';

@Component({
  selector: 'snjya-forgot-password',
  templateUrl: './forgot-password.component.html',
  styleUrls: ['./forgot-password.component.scss'],
})
export class ForgotPasswordComponent {
  public form: FormGroup = this.formBuilder.group({
    email: ['', [Validators.required, Validators.email]],
  });
  public submitted = false;
  public saving = false;
  public emailSent = false;
  email = '';

  constructor(
    private formBuilder: FormBuilder,
    private service: ForgotPasswordService,
    private _snackBar: ToastService,
    public Forgotservice: HomeService,
  ) { }


  @Input() data: any;

  imgPath: string = StoreFrontAPIConstant.IMG_URL;

  @Input() footerData: any = {};

  ngOnInit(): void {
    this.fetchData();
  }
  loading = false;

  fetchData() {
    this.loading = true;
    this.Forgotservice.getHomePageDetails().subscribe({
      next: (res: any) => {
        this.footerData = this.Forgotservice.getDataByComponentName(res?.data?.attributes?.Body || [], ComponentNameConstants.Footer);
        console.log(res);
        this.loading = false;
      }
    });
  }


  get f(): { [key: string]: AbstractControl } {
    return this.form.controls;
  }

  onSubmit(): void {
    this.submitted = true;

    if (this.form.invalid) {
      return;
    }
    this.email = this.form.value.email;
    this.saving = true;
    this.service.forgotPassword(this.form.value).subscribe({
      complete: () => {
        this.saving = false;
        this.emailSent = true;
        this._snackBar.open('Reset password link sent successfully!', {
          type: 'Success',
        });
      },
      error: (err) => {
        this.saving = false;
        this._snackBar.open(err?.error?.message || 'Error while processing your request.', {
          type: 'Error',
        });
      },
    });
  }

  onReset(): void {
    this.submitted = false;
    this.form.reset();
  }
}
