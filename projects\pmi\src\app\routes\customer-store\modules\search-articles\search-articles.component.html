<div class="d-flex w-100 h-100 justify-content-center align-items-center" *ngIf="loading">
    <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">Loading...</span>
    </div>
</div>
<ng-container *ngIf="!loading">
    <section class="all-inner-banner-sec position-relative">
        <!-- <div class="category-slider">
            <owl-carousel-o [options]="customOptions">
                <ng-container *ngFor="let slide of categoryBannerDetails">
                    <ng-template carouselSlide [id]="slide.id.toString()">
                        <div class="category-slider-img"
                            style="background: url('{{slide.attributes.url}}') center center no-repeat;"></div>
                    </ng-template>
                </ng-container>
            </owl-carousel-o>
        </div> -->
        <div class="all-inner-banner-body d-flex align-items-center">
            <div class="bedcrumbs m-0 p-0 d-flex position-absolute align-items-center"> <span
                    class="home-link cursor-pointer" routerLink="/store/home">Home</span>
                <span class="material-symbols-outlined">keyboard_arrow_right</span> Knowledge Article
            </div>
        </div>
    </section>
    <section class="article-listing-sec position-relative">
        <div class="article-listing-body position-relative d-flex justify-content-between">
            <div class="article-listing-left position-relative d-flex flex-column">
                <div class="article-listing-filter position-relative d-flex">
                    <div class="view-tab position-relative d-flex align-items-center justify-content-between">
                        <button type="button" class="view-btn d-flex align-items-center justify-content-center"
                            [class.active]="view == 'grid'" (click)="view = 'grid'"><span
                                class="material-symbols-outlined">grid_view</span></button>
                        <button type="button" class="view-btn d-flex align-items-center justify-content-center"
                            [class.active]="view == 'list'" (click)="view = 'list'"><span
                                class="material-symbols-outlined">format_list_bulleted</span></button>
                    </div>
                    <div class="short-list position-relative d-flex">
                        <label class="position-absolute d-flex align-items-center justify-content-center"><span
                                class="material-symbols-outlined">page_info</span></label>
                        <select class="form-control" [(ngModel)]="sort" (ngModelChange)="fetchArticles()">
                            <option selected disabled hidden>Sort by</option>
                            <option value="Heading:asc">Alphabetical (A-Z)</option>
                            <option value="Heading:desc">Alphabetical (Z-A)</option>
                            <option value="Views:desc">Most Viewed</option>
                            <option value="updatedAt:desc">Latest Date</option>
                        </select>
                    </div>
                    <div class="article-search-box position-relative d-flex">
                        <input type="search" class="form-control" placeholder="Search..." #searchText
                            (keyup)="onValueSearch(searchText)" (search)="onValueSearch(searchText)">
                        <label class="position-absolute d-flex align-items-center justify-content-center"><span
                                class="material-symbols-outlined">search</span></label>
                    </div>
                    <div class="page-size position-relative d-flex align-items-center">
                        <span>Articles per page</span>
                        <select class="form-control" [(ngModel)]="pageSize" (ngModelChange)="fetchArticles()">
                            <option *ngFor="let page of pageSizeOptions" [value]="page">{{ page }}</option>
                        </select>
                    </div>
                    <div class="show-post-list position-relative d-flex align-items-center"> Showing<span>{{
                            articles.length }}</span> of {{ totalCount }} Posts</div>
                </div>

                <div class="d-flex w-100 h-200px justify-content-center align-items-center" *ngIf="loadingArticles">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>
                <ng-container *ngIf="!loadingArticles">
                    <div class="article-listing position-relative d-grid" [class.list-view-grid]="view == 'list'">
                        <div class="article-box position-relative d-flex flex-column" *ngFor="let article of articles">
                            <div
                                class="article-box-img position-relative d-flex align-items-center justify-content-center">
                                <img [src]="article.attributes?.Banner_Image?.data?.attributes?.url" alt="" />
                                <div class="article-features position-absolute d-flex justify-content-between">
                                    <div class="article-date position-relative d-flex align-items-center"><span
                                            class="material-symbols-outlined">calendar_month</span> {{
                                        article.attributes?.updatedAt | date
                                        }}</div>
                                    <div class="article-views position-relative d-flex align-items-center"><span
                                            class="material-symbols-outlined">visibility</span> {{
                                        article.attributes?.Views
                                        }} views</div>
                                </div>
                            </div>

                            <div class="article-cnt position-relative d-flex flex-column" *ngIf="view != 'list'">
                                <div class="article-cnt-title position-relative">{{ article.attributes?.Heading }}</div>
                                <div class="article-cnt-p">{{ article.attributes?.Short_Description }}</div>
                                <a href="javascript: void(0)" class="position-relative d-flex align-items-center"
                                    (click)="openArticle(article.attributes.slug)">Read More
                                    <span class="material-symbols-outlined">east</span></a>
                            </div>

                            <div class="article-cnt cnt-list-view position-relative d-flex flex-column"
                                *ngIf="view == 'list'">
                                <div class="cnt-list-left">
                                    <div class="article-cnt-title position-relative">{{ article.attributes?.Heading }}
                                    </div>
                                    <div class="article-cnt-p">{{ article.attributes?.Short_Description }}</div>
                                </div>
                                <div class="cnt-list-right">
                                    <div class="article-date position-relative d-flex align-items-center"><span
                                            class="material-symbols-outlined">calendar_month</span> {{
                                        article.attributes?.updatedAt | date }}</div>
                                    <div class="article-views position-relative d-flex align-items-center"><span
                                            class="material-symbols-outlined">visibility</span> {{
                                        article.attributes?.Views
                                        }} views</div>
                                    <a href="javascript: void(0)" class="position-relative d-flex align-items-center"
                                        (click)="openArticle(article.attributes.slug)">Read More
                                        <span class="material-symbols-outlined">east</span></a>
                                </div>
                            </div>
                        </div>
                    </div>
                    <ngb-pagination [(page)]="currentPage" [pageSize]="pageSize" [collectionSize]="totalCount"
                        (pageChange)="onPageChange($event)"
                        class="position-relative d-flex align-items-center justify-content-center" />
                </ng-container>

                <!-- <button type="button" class="pagination-btn prev"><span
                            class="material-symbols-outlined">keyboard_arrow_left</span></button>
                    <div class="pag-count active">1</div>
                    <div class="pag-count">2</div>
                    <div class="pag-count">3</div>
                    <div class="pag-count">4</div>
                    <div class="pag-count">5</div>
                    <div class="pag-count">...</div>
                    <div class="pag-count">100</div>
                    <button type="button" class="pagination-btn next"><span
                            class="material-symbols-outlined">keyboard_arrow_right</span></button> -->
            </div>
        </div>
    </section>
</ng-container>