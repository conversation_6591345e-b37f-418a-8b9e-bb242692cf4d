<section class="bedcrumbs-sec">
    <div class="bedcrumbs m-0 d-flex align-items-center">
        <span routerLink="/store/home" class="home-link cursor-pointer">Home</span>
        <span class="material-symbols-outlined">keyboard_arrow_right</span>
        <span routerLink="/store/my-activation" class="home-link cursor-pointer">My Text Activations</span>
    </div>
</section>
<div class="d-flex w-100 h-100 justify-content-center align-items-center" *ngIf="loading">
    <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">Loading...</span>
    </div>
</div>
<section class="my-activation-sec" *ngIf="!loading">
    <div class="my-activation-body">
        <div class="my-activation-header-sec">
            <div class="my-activation-header-top d-flex align-items-center justify-content-between">
                <div class="request d-flex align-items-center">
                    <div class="request-img d-flex align-items-center justify-content-center"> <img
                            src="/assets/images/act-request.svg" alt="" /> </div>
                    <div class="request-cnt">
                        <h5>Activation Request</h5>
                        <h3>{{ ticket.TICKET_ID }}</h3>
                    </div>
                </div>
            </div>
            <div class="my-activation-header-bottom d-flex">
                <div class="my-activation-header-box d-flex flex-column align-items-center">
                    <span class="material-symbols-outlined">account_circle</span>
                    <h4>Email</h4>
                    <h5>{{ sellerDetails.email }}</h5>
                </div>
                <div class="my-activation-header-box d-flex flex-column align-items-center">
                    <span class="material-symbols-outlined">event</span>
                    <h4>Created Date</h4>
                    <h5>{{ ticket.CREATED_ON | date }}</h5>
                </div>
                <div class="my-activation-header-box d-flex flex-column align-items-center">
                    <span class="material-symbols-outlined">autorenew</span>
                    <h4>Status</h4>
                    <h5>{{ ticket.STATUS_TEXT }}</h5>
                </div>
                <div class="my-activation-header-box d-flex flex-grow-2 flex-column align-items-center">
                    <span class="material-symbols-outlined">local_activity</span>
                    <h4>Subject</h4>
                    <h5>{{ ticket.SUBJECT }}</h5>
                </div>
            </div>
        </div>

        <div class="my-activation-form-sec">
            <div class="my-activation-form-tab d-flex align-items-center">
                <button type="button" class="tab-btn d-flex align-items-center justify-content-center selected"><span
                        class="material-symbols-outlined">article</span> Details</button>
            </div>
            <div class="my-activation-form-body">
                <form class="d-flex flex-wrap">
                    <div class="form-group">
                        <label>Email</label>
                        <p>{{ sellerDetails.email }}</p>
                    </div>
                    <div class="form-group">
                        <label>Priority</label>
                        <p>{{ ticket.PRORITY_TEXT }}</p>
                    </div>
                    <div class="form-group">
                        <label>Status</label>
                        <p>{{ ticket.STATUS_TEXT }}</p>
                    </div>
                    <div class="form-group">
                        <label>Subject</label>
                        <p>{{ ticket.SUBJECT }}</p>
                    </div>
                    <div class="form-group full-width">
                        <label>Message Text</label>
                        <textarea class="form-control" *ngIf="ticket.NOTES.length"
                            [value]="ticket.NOTES[0].TEXT"></textarea>
                    </div>
                    <div class="form-group user-box d-flex align-items-center">
                        <div class="user-img d-flex align-items-center justify-content-center"> <img
                                src="/assets/images/person.svg" alt="" /> </div>
                        <div class="user-cnt">
                            <h5>Created Date</h5>
                            <h3>{{ ticket.CREATED_ON | date}}</h3>
                        </div>
                    </div>
                    <div class="form-group user-box d-flex align-items-center">
                        <div class="user-img d-flex align-items-center justify-content-center"> <img
                                src="/assets/images/person.svg" alt="" /> </div>
                        <div class="user-cnt">
                            <h5>Updated Date</h5>
                            <h3>{{ ticket.CHANGED_ON | date}}</h3>
                        </div>
                    </div>
                </form>
            </div>
        </div>

    </div>
</section>