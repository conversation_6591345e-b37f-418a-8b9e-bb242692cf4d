<section class="newsfeed-sec">
    <div class="news-feed-body">
        <div class="newsfeed-title-sec position-relative d-flex justify-content-between">
            <div class="news-feed-title">{{data?.Title}}</div>
            <div class="news-feed-desc">{{data?.Description}}</div>
        </div>
        <div class="newdfeed-list d-grid position-relative">
            <ng-container *ngFor="let newsFeedBox of data?.News_Feeds_Box">
                <div class="newsfeed-box">
                    <a [routerLink]="[newsFeedBox?.Navigation_Link]">
                        <div class="news-f-img">
                            <img [src]="[newsFeedBox?.Images.data.attributes.url]" alt="" />
                        </div>
                        <div class="news-f-cnt d-flex flex-column">
                            <div class="news-f-box-title">{{newsFeedBox?.Title}}</div>
                            <div class="news-f-link d-flex align-items-center justify-content-center">
                                {{newsFeedBox?.Navigation_text}}<span
                                    class="material-symbols-outlined">{{newsFeedBox?.Navigation_Icon}}</span></div>
                        </div>
                    </a>
                </div>
            </ng-container>
        </div>

        <div class="news-list" style="margin-top: 30px;"></div>

        <!-- <owl-carousel-o [options]="customOptions">
            <ng-template carouselSlide>
                <div class="news-box">
                    <img src="/assets/images/news-feed-img.jpg" alt="" />
                    <h3>Title 1</h3>
                    <p>Sed ut perspiciatis unde omnis iste natus error sit voluptatem</p>
                </div>
            </ng-template>
            <ng-template carouselSlide>
                <div class="news-box">
                    <img src="/assets/images/news-feed-img.jpg" alt="" />
                    <h3>Title 2</h3>
                    <p>Sed ut perspiciatis unde omnis iste natus error sit voluptatem</p>
                </div>
            </ng-template>
            <ng-template carouselSlide>
                <div class="news-box">
                    <img src="/assets/images/news-feed-img.jpg" alt="" />
                    <h3>Title 3</h3>
                    <p>Sed ut perspiciatis unde omnis iste natus error sit voluptatem</p>
                </div>
            </ng-template>
            <ng-template carouselSlide>
                <div class="news-box">
                    <img src="/assets/images/news-feed-img.jpg" alt="" />
                    <h3>Title 4</h3>
                    <p>Sed ut perspiciatis unde omnis iste natus error sit voluptatem</p>
                </div>
            </ng-template>
            <ng-template carouselSlide>
                <div class="news-box">
                    <img src="/assets/images/news-feed-img.jpg" alt="" />
                    <h3>Title 5</h3>
                    <p>Sed ut perspiciatis unde omnis iste natus error sit voluptatem</p>
                </div>
            </ng-template>
        </owl-carousel-o> -->

    </div>
</section>